import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/di/dependency_injection.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';

import 'core/config/theme/app_theme.dart';
import 'features/authentication/presentation/bloc/authentication_bloc.dart';
import 'features/dashboard/presentation/bloc/dashboard_bloc.dart';
import 'features/inventory/presentation/bloc/inventory_bloc.dart';
import 'features/order/presentation/bloc/order_bloc.dart';
import 'features/shared/presentation/bloc/bottom-nav-cubit/bottom_nav_cubit.dart';
import 'features/shared/presentation/bloc/theme-cubit/theme_cubit.dart';
import 'features/shared/presentation/pages/splash_page.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthenticationBloc>(
          create: (context) =>
              sl<AuthenticationBloc>()..add(CheckUserAuthenticationEvent()),
        ),
        BlocProvider<UserBloc>(
          create: (context) => sl<UserBloc>()..add(const GetCurrentUserEvent()),
        ),
        BlocProvider<DashboardBloc>(create: (context) => sl<DashboardBloc>()),
        BlocProvider<OrderBloc>(create: (context) => sl<OrderBloc>()),
        BlocProvider<InventoryBloc>(create: (context) => sl<InventoryBloc>()),

        //
        BlocProvider<BottomNavCubit>(create: (context) => sl<BottomNavCubit>()),
        BlocProvider<ThemeCubit>(create: (context) => sl<ThemeCubit>()),
        //     BlocProvider(
        //       create: (context) => sl<SharedBloc>(),
        //     ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return BlocBuilder<ThemeCubit, ThemeMode>(
            builder: (context, themeMode) {
              return MaterialApp(
                debugShowCheckedModeBanner: false,
                title: 'Gas System',
                theme: AppTheme.light,
                darkTheme: AppTheme.dark,
                // themeMode: ThemeMode.light,
                themeMode: themeMode,
                home: const SplashPage(),
              );
            },
          );
        },
      ),
    );
  }
}
