part of 'user_bloc.dart';

abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

class RegisterAdminOrAgentEvent extends UserEvent {
  final String phone;
  final String? email;
  final String password;
  final UserRole role;
  final VehicleType vehicleType;
  final String vehicleNumber;

  const RegisterAdminOrAgentEvent({
    required this.phone,
    this.email,
    required this.password,
    required this.role,
    required this.vehicleType,
    required this.vehicleNumber,
  });

  @override
  List<Object?> get props => [
    phone,
    email,
    password,
    role,
    vehicleType,
    vehicleNumber,
  ];
}

class UpdateUserEvent extends UserEvent {
  final String userId;
  final String? email;
  final String? username;
  final String? password;
  final List<UpdateAddressParams>? addresses;
  final bool? isActive;
  final UpdateAgentMetadataParams? agentMetadata;

  const UpdateUserEvent({
    required this.userId,
    this.email,
    this.username,
    this.password,
    this.addresses,
    this.isActive,
    this.agentMetadata,
  });

  @override
  List<Object?> get props => [
    userId,
    email,
    username,
    password,
    addresses,
    isActive,
    agentMetadata,
  ];
}

class DeleteUserEvent extends UserEvent {
  final String userId;

  const DeleteUserEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class ChangeUserRoleEvent extends UserEvent {
  final String userId;
  final UserRole newRole;

  const ChangeUserRoleEvent({required this.userId, required this.newRole});

  @override
  List<Object?> get props => [userId, newRole];
}

class SubscribeToNotificationsEvent extends UserEvent {
  final String userId;
  final String topic;
  final String deviceToken;

  const SubscribeToNotificationsEvent({
    required this.userId,
    required this.topic,
    required this.deviceToken,
  });

  @override
  List<Object?> get props => [userId, topic, deviceToken];
}

class UnsubscribeFromNotificationsEvent extends UserEvent {
  final String userId;
  final String topic;
  final String deviceToken;

  const UnsubscribeFromNotificationsEvent({
    required this.userId,
    required this.topic,
    required this.deviceToken,
  });

  @override
  List<Object?> get props => [userId, topic, deviceToken];
}

class GetAllUsersEvent extends UserEvent {
  final UserRole? role;
  final bool? isActive;
  final int? page;
  final int? limit;
  final String? sortBy;
  final String? sortOrder;

  const GetAllUsersEvent({
    this.role,
    this.isActive,
    this.page,
    this.limit,
    this.sortBy,
    this.sortOrder,
  });

  @override
  List<Object?> get props => [role, isActive, page, limit, sortBy, sortOrder];
}

class GetCurrentUserEvent extends UserEvent {
  const GetCurrentUserEvent();
}
