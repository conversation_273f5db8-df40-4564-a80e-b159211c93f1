// get_all_users_usecase.dart
import 'package:fpdart/fpdart.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../params/get_all_users_params.dart';
import '../repository/user_repository.dart';

class GetAllUsersUseCase implements UseCase<List<UserEntity>, GetAllUsersParams> {
  final UserRepository repository;

  const GetAllUsersUseCase({required this.repository});

  @override
  Future<Either<AppFailure, List<UserEntity>>> call({
    required GetAllUsersParams params,
  }) async {
    return await repository.getAllUsers(
      role: params.role,
      isActive: params.isActive,
      page: params.page,
      limit: params.limit,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    );
  }
}