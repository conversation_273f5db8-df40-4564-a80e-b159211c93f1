import 'package:fpdart/fpdart.dart';
import 'package:frontend/features/authentication/data/models/user_model.dart';

import '../../../../../core/constants/api_end_points.dart';
import '../../../../../core/enums/http_method.dart';
import '../../../../../core/enums/user_role.dart';
import '../../../../../core/enums/vehicle_type.dart';
import '../../../../../core/errors/app_failure.dart';
import '../../../../../core/errors/http_error_handler.dart';
import '../../../../../core/network/api_client/dio_api_client.dart';
import '../../../../../core/utils/helpers/request_data.dart';
import '../../../domain/params/update_user_params.dart';
import '../../../domain/params/change_user_role_params.dart';

abstract class UserRemoteDatasource {
  FutureEitherFailOr<void> registerAdminOrAgent({
    required String phone,
    String? email,
    required String password,
    required UserRole role,
    required VehicleType vehicleType,
    required String vehicleNumber,
  });

  FutureEitherFailOr<void> updateUser({
    required String userId,
    String? email,
    String? username,
    String? password,
    List<UpdateAddressParams>? addresses,
    bool? isActive,
    UpdateAgentMetadataParams? agentMetadata,
  });

  FutureEitherFailOr<void> deleteUser({required String userId});

  FutureEitherFailOr<String> changeUserRole({
    required String userId,
    required UserRole newRole,
  });

  FutureEitherFailOr<void> subscribeToNotifications({
    required String userId,
    required String topic,
    required String deviceToken,
  });

  FutureEitherFailOr<void> unsubscribeFromNotifications({
    required String userId,
    required String topic,
    required String deviceToken,
  });

  FutureEitherFailOr<List<UserModel>> getAllUsers({
    UserRole? role,
    bool? isActive,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
  });

  FutureEitherFailOr<UserModel> getCurrentUser();
}

class UserRemoteDatasourceImpl implements UserRemoteDatasource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const UserRemoteDatasourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<void> registerAdminOrAgent({
    required String phone,
    String? email,
    required String password,
    required UserRole role,
    required VehicleType vehicleType,
    required String vehicleNumber,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.registerAdminOrAgent,
        data: RequestData.json({
          'phone': phone,
          if (email != null) 'email': email,
          'password': password,
          'role': role.name,
          'vehicleType': vehicleType.value,
          'vehicleNumber': vehicleNumber,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> updateUser({
    required String userId,
    String? email,
    String? username,
    String? password,
    List<UpdateAddressParams>? addresses,
    bool? isActive,
    UpdateAgentMetadataParams? agentMetadata,
  }) async {
    final Map<String, dynamic> requestData = {};

    if (email != null) requestData['email'] = email;
    if (username != null) requestData['username'] = username;
    if (password != null) requestData['password'] = password;
    if (isActive != null) requestData['isActive'] = isActive;

    if (addresses != null && addresses.isNotEmpty) {
      requestData['addresses'] = addresses
          .map((addr) => addr.toJson())
          .toList();
    }

    if (agentMetadata != null) {
      requestData['agentMetadata'] = agentMetadata.toJson();
    }

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndpoints.updateUserById(userId),
        data: RequestData.json(requestData),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> deleteUser({required String userId}) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: ApiEndpoints.deleteUserById(userId),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> subscribeToNotifications({
    required String userId,
    required String topic,
    required String deviceToken,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.subscribeNotifications,
        data: RequestData.json({
          'userId': userId,
          'topic': topic,
          'deviceToken': deviceToken,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<void> unsubscribeFromNotifications({
    required String userId,
    required String topic,
    required String deviceToken,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.unsubscribeNotifications,
        data: RequestData.json({
          'userId': userId,
          'topic': topic,
          'deviceToken': deviceToken,
        }),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      return right(null);
    });
  }

  @override
  FutureEitherFailOr<List<UserModel>> getAllUsers({
    UserRole? role,
    bool? isActive,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.allUsers,
        queryParameters: {
          if (role != null) 'role': role.name,
          if (isActive != null) 'isActive': isActive.toString(),
          if (page != null) 'page': page.toString(),
          if (limit != null) 'limit': limit.toString(),
          if (sortBy != null) 'sortBy': sortBy,
          if (sortOrder != null) 'sortOrder': sortOrder,
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final users = apiResponse.getNonNullableData();
      return right(users);
    });
  }

  @override
  FutureEitherFailOr<UserModel> getCurrentUser() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getCurrentUser,
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final user = apiResponse.getNonNullableData();
      return right(user);
    });
  }

  @override
  FutureEitherFailOr<String> changeUserRole({
    required String userId,
    required UserRole newRole,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.patch,
        endPointUrl: ApiEndpoints.changeUserRole(userId),
        data: RequestData.json({'newRole': newRole.name}),
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      // final user = apiResponse.getNonNullableData();
      final message = apiResponse.apiMessage;
      return right(message);
    });
  }
}
