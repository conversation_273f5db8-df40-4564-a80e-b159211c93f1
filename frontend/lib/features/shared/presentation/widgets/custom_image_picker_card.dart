import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:image_picker/image_picker.dart';

import 'image_place_holder.dart';

class CustomImagePickerCard extends StatelessWidget {
  final Function(XFile pickedImage)? onCamera;
  final Function(XFile pickedImage)? onGallery;
  final bool isLoading;
  final XFile? imageFile;
  final String? imageUrl;
  final double radius;
  final bool showIcon;
  final BoxFit? boxFit;
  // final ImageType imageType;
  final String? userName;

  const CustomImagePickerCard({
    super.key,
    this.onCamera,
    this.onGallery,
    this.isLoading = true,
    this.imageFile,
    this.imageUrl,
    this.radius = 50.0, // Default radius
    this.showIcon = false,
    this.boxFit = BoxFit.cover,
    // this.imageType = ImageType.other,
    this.userName,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: isLoading
            ? null
            : () {
                final picker = ImagePicker();
                showImagePickerDialog(
                  context: context,
                  // onCamera: () => onCamera?.call(),
                  onCamera: () async {
                    Navigator.pop(context);
                    final selectedImage = await picker.pickImage(
                      source: ImageSource.camera,
                    );
                    if (selectedImage != null) {
                      onCamera?.call(selectedImage);
                    }
                  },
                  // onGallery: () => onGallery?.call(),
                  onGallery: () async {
                    Navigator.pop(context);
                    final selectedImage = await picker.pickImage(
                      source: ImageSource.gallery,
                    );
                    if (selectedImage != null) {
                      onGallery?.call(selectedImage);
                    }
                  },
                );
              },
        child: CircleAvatar(
          backgroundColor: context.appColors.disabledColor,
          radius: radius.r, // Use the customizable radius
          child: Stack(
            children: [
              _buildImageContent(context), // Handle image priority logic
              if (showIcon) _buildAddPhotoIcon(), // Conditionally show the icon
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageContent(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 2000),
      reverseDuration: const Duration(milliseconds: 4000),
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(scale: animation, child: child),
        );
      },
      child: _buildImageWidget(context),
    );
  }

  Widget _buildImageWidget(BuildContext context) {
    if (imageFile != null) {
      return ClipOval(
        key: ValueKey('file_${imageFile!.path}'),
        child: SizedBox(
          width: radius * 2.w,
          height: radius * 2.h,
          child: Image.file(File(imageFile!.path), fit: boxFit),
        ),
      );
    } else if (imageUrl != null && imageUrl!.isNotEmpty) {
      return ClipOval(
        key: ValueKey('url_$imageUrl'),
        child: SizedBox(
          width: radius * 2.w,
          height: radius * 2.h,
          child: CachedNetworkImage(
            imageUrl: imageUrl!,
            placeholder: (context, url) => const ImagePlaceholder(),
            errorWidget: (context, url, error) {
              // if (imageType == ImageType.doctor) {
              //   return Image.asset(
              //     Assets.images.png.doctor.path,
              //     fit: boxFit,
              //   );
              // }
              // // if (_shouldShowInitials()) {
              // //   return _buildInitials(context);
              // // }
              // if (imageType == ImageType.profile) {
              //   return Image.network(
              //     // Assets.images.png.doctor.path,
              //     'https://cdn-icons-png.flaticon.com/128/149/149071.png',
              //     fit: boxFit,
              //   );
              // }

              return Icon(
                Icons.camera_alt,
                size: radius.w,
                color: context.appColors.whiteColor,
              );
            },
            fit: boxFit,
          ),
        ),
      );
    } else {
      // if (imageType == ImageType.doctor) {
      //   return Image.asset(
      //     Assets.images.png.doctor.path,
      //     fit: boxFit,
      //     key: ValueKey('asset_${Assets.images.png.doctor.path}'),
      //   );
      // }
      // // if (_shouldShowInitials()) {
      // //   return _buildInitials(context);
      // // }
      // if (imageType == ImageType.profile) {
      //   return Image.network(
      //     // Assets.images.png.doctor.path,
      //     'https://cdn-icons-png.flaticon.com/128/149/149071.png',
      //     fit: boxFit,
      //   );
      // }
      return Icon(
        Icons.image,
        size: radius.w,
        color: context.appColors.whiteColor,
        key: const ValueKey('default_icon'),
      );
    }
  }

  // Helper method to build the "Add Photo" icon
  Widget _buildAddPhotoIcon() {
    return Positioned(
      right: 3,
      bottom: 1,
      child: Container(
        padding: EdgeInsets.all(5.w),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [BoxShadow(spreadRadius: 1, blurRadius: 1)],
        ),
        child: Icon(Icons.add_a_photo, color: Colors.grey.shade900),
      ),
    );
  }

  void showImagePickerDialog({
    required BuildContext context,
    required Function() onGallery,
    required Function() onCamera,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog.adaptive(
          content: Padding(
            padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 20.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTap: onCamera,
                      child: Column(
                        children: [
                          Icon(Icons.photo_camera, size: 50.w),
                          SizedBox(height: 10.w),
                          const Text('Camera'),
                        ],
                      ),
                    ),
                    SizedBox(width: 30.w),
                    GestureDetector(
                      onTap: onGallery,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.photo, size: 50.w),
                          SizedBox(height: 10.w),
                          const Text('Gallery'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // bool _shouldShowInitials() {
  //   return imageType == ImageType.profile &&
  //       userName != null &&
  //       userName!.trim().isNotEmpty;
  // }

  // Widget _buildInitials(BuildContext context) {
  //   final appColors = context.appColors;
  //   final initials = _getInitials(userName ?? '');

  //   return Container(
  //     width: radius * 2.w,
  //     height: radius * 2.h,
  //     decoration: BoxDecoration(
  //       color: appColors.subtextColor.withValues(alpha: 0.1),
  //       shape: BoxShape.circle,
  //     ),
  //     child: Center(
  //       child: Text(
  //         initials,
  //         style: TextStyle(
  //           fontSize: radius.w * 0.8,
  //           color: appColors.whiteColor,
  //           fontWeight: FontWeight.bold,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // String _getInitials(String name) {
  //   final words = name.trim().split(' ');
  //   if (words.length == 1) {
  //     return words[0][0].toUpperCase();
  //   } else {
  //     return (words[0][0] + words[1][0]).toUpperCase();
  //   }
  // }
}
