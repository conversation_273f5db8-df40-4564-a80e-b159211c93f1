import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/enums/cylinder_material.dart';
import 'package:frontend/core/enums/cylinder_status.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/inventory/domain/entities/cylinder_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:image_picker/image_picker.dart';

class CreateCylinderPage extends StatefulWidget {
  final bool isEditMode;
  final CylinderEntity? existingCylinder;

  const CreateCylinderPage({
    super.key,
    this.isEditMode = false,
    this.existingCylinder,
  });

  @override
  State<CreateCylinderPage> createState() => _CreateCylinderPageState();
}

class _CreateCylinderPageState extends State<CreateCylinderPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minimumStockController = TextEditingController();
  // final _imageUrlController = TextEditingController();

  CylinderType? _selectedType;
  CylinderMaterial? _selectedMaterial;
  CylinderStatus _selectedStatus = CylinderStatus.active;
  XFile? _selectedImageFile;

  @override
  void initState() {
    super.initState();
    if (widget.isEditMode && widget.existingCylinder != null) {
      _initializeEditMode();
    }
  }

  void _initializeEditMode() {
    final cylinder = widget.existingCylinder!;
    _nameController.text = '${cylinder.type.name} - ${cylinder.material.name}';
    _descriptionController.text = cylinder.description;
    _priceController.text = cylinder.price.toString();
    _costController.text = cylinder.cost.toString();
    _quantityController.text = cylinder.stock.toString();
    _minimumStockController.text = cylinder.minimumStockLevel.toString();

    _selectedType = cylinder.type;
    _selectedMaterial = cylinder.material;
    _selectedStatus = cylinder.status;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _quantityController.dispose();
    _minimumStockController.dispose();
    // _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        title: Text(
          widget.isEditMode ? 'Edit Cylinder' : 'Create New Cylinder',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _resetForm,
            child: Text(
              'Reset',
              style: TextStyle(
                color: context.appColors.subtextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is CreateCylinderSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Cylinder created successfully!'),
                backgroundColor: context.appColors.successColor,
              ),
            );
            Navigator.of(context).pop();
          } else if (state is CreateCylinderFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Failed to create cylinder: ${state.appFailure.getErrorMessage()}',
                ),
                backgroundColor: context.appColors.errorColor,
              ),
            );
          } else if (state is UpdateCylinderSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Cylinder updated successfully!'),
                backgroundColor: context.appColors.successColor,
              ),
            );
            Navigator.of(context).pop();
          } else if (state is UpdateCylinderFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Failed to update cylinder: ${state.appFailure.getErrorMessage()}',
                ),
                backgroundColor: context.appColors.errorColor,
              ),
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Basic Information'),
                SizedBox(height: 16.h),
                _buildTypeAndMaterialRow(),
                SizedBox(height: 16.h),
                _buildDescriptionField(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Product Image'),
                SizedBox(height: 16.h),
                _buildImagePickerSection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Pricing'),
                SizedBox(height: 16.h),
                _buildPricingRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Stock Management'),
                SizedBox(height: 16.h),
                _buildStockRow(),
                SizedBox(height: 16.h),
                _buildStatusField(),
                SizedBox(height: 24.h),
                // _buildSectionHeader('Additional Information'),
                // SizedBox(height: 16.h),
                // _buildImageUrlField(),
                SizedBox(height: 32.h),
                _buildCreateButton(),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: context.appColors.textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildTypeAndMaterialRow() {
    return Row(
      children: [
        Expanded(
          child: _buildDropdownField<CylinderType>(
            label: 'Cylinder Type',
            value: _selectedType,
            items: CylinderType.values,
            onChanged: (value) => setState(() => _selectedType = value),
            itemBuilder: (type) => type.displayName,
            validator: (value) => value == null ? 'Please select a type' : null,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildDropdownField<CylinderMaterial>(
            label: 'Material',
            value: _selectedMaterial,
            items: CylinderMaterial.values,
            onChanged: (value) => setState(() => _selectedMaterial = value),
            itemBuilder: (material) => material.displayName,
            validator: (value) =>
                value == null ? 'Please select a material' : null,
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Description',
        hintText: 'Enter cylinder description',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a description';
        }
        return null;
      },
    );
  }

  Widget _buildImagePickerSection() {
    return Center(
      child: Column(
        children: [
          CustomImagePickerCard(
            imageFile: _selectedImageFile,
            imageUrl:
                widget.isEditMode &&
                    widget.existingCylinder != null &&
                    widget.existingCylinder!.imageUrl.isNotEmpty
                ? widget.existingCylinder!.formattedImageUrl
                : null,
            radius: 60.0,
            showIcon: true,
            isLoading: false,
            onCamera: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
            onGallery: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
          ),
          SizedBox(height: 12.h),
          Text(
            widget.isEditMode
                ? 'Tap to change cylinder image'
                : 'Tap to add cylinder image',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          if (_selectedImageFile != null) ...[
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  color: context.appColors.successColor,
                  size: 16.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  'Image selected',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.successColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedImageFile = null;
                    });
                  },
                  child: Icon(
                    Icons.close,
                    color: context.appColors.errorColor,
                    size: 16.w,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPricingRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _priceController,
            label: 'Selling Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final price = double.tryParse(value);
              if (price == null || price <= 0) return 'Invalid price';
              final cost = double.tryParse(_costController.text);
              if (cost != null && price < cost) return 'Price must be ≥ cost';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _costController,
            label: 'Cost Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final cost = double.tryParse(value);
              if (cost == null || cost < 0) return 'Invalid cost';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStockRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _quantityController,
            label: 'Initial Quantity',
            hint: '0',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final quantity = int.tryParse(value);
              if (quantity == null || quantity < 0) return 'Invalid quantity';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _minimumStockController,
            label: 'Minimum Stock Level',
            hint: '10',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final minStock = int.tryParse(value);
              if (minStock == null || minStock < 0) return 'Invalid value';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatusField() {
    return _buildDropdownField<CylinderStatus>(
      label: 'Status',
      value: _selectedStatus,
      items: CylinderStatus.values,
      onChanged: (value) => setState(() => _selectedStatus = value!),
      itemBuilder: (status) => status.displayName,
    );
  }

  // Widget _buildImageUrlField() {
  //   return TextFormField(
  //     controller: _imageUrlController,
  //     decoration: InputDecoration(
  //       labelText: 'Image URL (Optional)',
  //       hintText: 'https://example.com/image.jpg',
  //       prefixIcon: Icon(Icons.image, color: context.appColors.primaryColor),
  //       border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
  //       enabledBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.r),
  //         borderSide: BorderSide(color: context.appColors.dividerColor),
  //       ),
  //       focusedBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(8.r),
  //         borderSide: BorderSide(color: context.appColors.primaryColor),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildDropdownField<T>({
    required String label,
    required T? value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
    required String Function(T) itemBuilder,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      items: items.map((item) {
        return DropdownMenuItem<T>(value: item, child: Text(itemBuilder(item)));
      }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
    // return CustomDropDown<T>(
    //   value: value,
    //   items: items,
    //   onChanged: onChanged,
    //   displayItem: (item) => itemBuilder(item),
    //   labelText: label,
    //   validator: validator,
    // );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? prefix,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefix,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildCreateButton() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        final isLoading =
            state is CreateCylinderLoading || state is UpdateCylinderLoading;

        return SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.isEditMode ? 'Update Cylinder' : 'Create Cylinder',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (widget.isEditMode) {
        // Update existing cylinder
        final event = UpdateCylinderEvent(
          cylinderId: widget.existingCylinder!.id,
          type: _selectedType,
          material: _selectedMaterial,
          price: double.parse(_priceController.text),
          cost: double.parse(_costController.text),
          description: _descriptionController.text.trim(),
          minimumStockLevel: int.parse(_minimumStockController.text),
          status: _selectedStatus,
          imageFile: _selectedImageFile,
        );
        context.read<InventoryBloc>().add(event);
      } else {
        // Create new cylinder
        final event = CreateCylinderEvent(
          type: _selectedType!,
          material: _selectedMaterial!,
          price: double.parse(_priceController.text),
          cost: double.parse(_costController.text),
          description: _descriptionController.text.trim(),
          quantity: int.parse(_quantityController.text),
          minimumStockLevel: int.parse(_minimumStockController.text),
          status: _selectedStatus,
          imageFile: _selectedImageFile,
        );
        context.read<InventoryBloc>().add(event);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _costController.clear();
    _quantityController.clear();
    _minimumStockController.clear();
    setState(() {
      _selectedType = null;
      _selectedMaterial = null;
      _selectedStatus = CylinderStatus.active;
      _selectedImageFile = null;
    });
  }
}
