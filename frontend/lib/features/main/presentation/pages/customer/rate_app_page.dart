import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/button_state.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_button.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:url_launcher/url_launcher.dart';

class RateAppPage extends StatefulWidget {
  const RateAppPage({super.key});

  @override
  State<RateAppPage> createState() => _RateAppPageState();
}

class _RateAppPageState extends State<RateAppPage>
    with TickerProviderStateMixin {
  int _selectedRating = 0;
  final TextEditingController _feedbackController = TextEditingController();
  bool _isSubmitting = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  final List<RatingOption> _ratingOptions = [
    RatingOption(
      rating: 1,
      title: 'Poor',
      subtitle: 'Really disappointed',
      color: Colors.red,
      emoji: '😞',
    ),
    RatingOption(
      rating: 2,
      title: 'Fair',
      subtitle: 'Not quite what I expected',
      color: Colors.orange,
      emoji: '😐',
    ),
    RatingOption(
      rating: 3,
      title: 'Good',
      subtitle: 'It\'s okay',
      color: Colors.amber,
      emoji: '🙂',
    ),
    RatingOption(
      rating: 4,
      title: 'Very Good',
      subtitle: 'I like it',
      color: Colors.lightGreen,
      emoji: '😊',
    ),
    RatingOption(
      rating: 5,
      title: 'Excellent',
      subtitle: 'I love it!',
      color: Colors.green,
      emoji: '🤩',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        title: Text(
          'Rate Our App',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildAppIcon(),
            SizedBox(height: 24.h),
            _buildRatingTitle(),
            SizedBox(height: 32.h),
            _buildStarRating(),
            SizedBox(height: 24.h),
            if (_selectedRating > 0) ...[
              _buildRatingFeedback(),
              SizedBox(height: 24.h),
              _buildFeedbackForm(),
              SizedBox(height: 24.h),
              _buildActionButtons(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAppIcon() {
    return Container(
      width: 100.w,
      height: 100.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.appColors.primaryColor,
            context.appColors.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: context.appColors.primaryColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Icon(Icons.local_gas_station, size: 50.sp, color: Colors.white),
    );
  }

  Widget _buildRatingTitle() {
    return Column(
      children: [
        Text(
          'How was your experience?',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.h),
        Text(
          'Your feedback helps us improve our service',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.appColors.textColor.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStarRating() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        final starIndex = index + 1;
        final isSelected = starIndex <= _selectedRating;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedRating = starIndex;
            });
            _animationController.forward().then((_) {
              _animationController.reverse();
            });
          },
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: isSelected && starIndex == _selectedRating
                    ? _scaleAnimation.value
                    : 1.0,
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  child: Icon(
                    isSelected ? Icons.star : Icons.star_border,
                    size: 40.sp,
                    color: isSelected
                        ? Colors.amber
                        : context.appColors.textColor.withValues(alpha: 0.3),
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildRatingFeedback() {
    final selectedOption = _ratingOptions.firstWhere(
      (option) => option.rating == _selectedRating,
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: selectedOption.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: selectedOption.color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(selectedOption.emoji, style: TextStyle(fontSize: 40.sp)),
          SizedBox(height: 8.h),
          Text(
            selectedOption.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: selectedOption.color,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            selectedOption.subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackForm() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tell us more (Optional)',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Share your thoughts to help us improve',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.textColor.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: 16.h),
          CustomTextField(
            controller: _feedbackController,
            maxLine: 4,
            hintText: 'What did you like or dislike? Any suggestions?',
            borderRadius: 8.0,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        CustomButton.filled(
          onTap: _isSubmitting ? null : _submitRating,
          buttonText: 'Submit Rating',
          buttonState: _isSubmitting ? ButtonState.loading : ButtonState.normal,
          width: double.infinity,
          height: 48.0,
          borderRadius: 8.0,
          backgroundColor: context.appColors.primaryColor,
        ),
        SizedBox(height: 12.h),
        if (_selectedRating >= 4) ...[
          CustomButton.outline(
            onTap: _openAppStore,
            buttonText: 'Rate on App Store',
            leadingIcon: Icon(
              Icons.store,
              color: context.appColors.primaryColor,
              size: 20.sp,
            ),
            width: double.infinity,
            height: 48.0,
            borderRadius: 8.0,
            isBorderActive: true,
          ),
        ],
      ],
    );
  }

  void _submitRating() async {
    setState(() {
      _isSubmitting = true;
    });

    // Simulate rating submission
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isSubmitting = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Thank you for your feedback!'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context);
    }
  }

  void _openAppStore() async {
    // For demo purposes, we'll show a dialog
    // In a real app, you would use the app's actual store URLs
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rate on App Store'),
        content: const Text(
          'This would open the app store for rating. Feature coming soon!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class RatingOption {
  final int rating;
  final String title;
  final String subtitle;
  final Color color;
  final String emoji;

  RatingOption({
    required this.rating,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.emoji,
  });
}
