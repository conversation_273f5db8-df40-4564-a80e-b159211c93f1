import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../domain/entities/package_item_entity.dart';

class PackageItemModel extends PackageItemEntity {
  PackageItemModel({
    required super.sparePartId,
    required super.name,
    required super.quantity,
    required super.unitPrice,
    required super.totalPrice,
  });

  factory PackageItemModel.fromJson(Map<String, dynamic> json) {
    try {
      // return PackageItemModel(
      //   sparePartId: json['sparePartId'] ?? '',
      //   name: json['name'] ?? '',
      //   quantity: json['quantity'] ?? 0,
      //   unitPrice: (json['unitPrice'] as num).toDouble(),
      //   totalPrice: (json['totalPrice'] as num).toDouble(),
      // );
      return PackageItemModel(
        sparePartId: json['part'] is String
            ? json['part']
            : json['part']['_id'] ?? '',
        name:
            json['name'] ??
            json['part']?['description'] ??
            '', // fallback to part description
        quantity: json['quantity'] ?? 0,
        unitPrice: (json['unitPrice'] ?? json['part']?['price'] ?? 0)
            .toDouble(), // fallback to part price
        totalPrice:
            (json['totalPrice'] ??
                    (json['unitPrice'] ?? json['part']?['price'] ?? 0) *
                        (json['quantity'] ?? 1))
                .toDouble(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse PackageItemModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PackageItemModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'sparePartId': sparePartId,
      'name': name,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  static List<PackageItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => PackageItemModel.fromJson(json)).toList();
  }
}
