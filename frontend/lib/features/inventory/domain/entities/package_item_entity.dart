class PackageItemEntity {
  final String sparePartId;
  final String name;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  PackageItemEntity({
    required this.sparePartId,
    required this.name,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  PackageItemEntity copyWith({
    String? sparePartId,
    String? name,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return PackageItemEntity(
      sparePartId: sparePartId ?? this.sparePartId,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PackageItemEntity && other.sparePartId == sparePartId;
  }

  @override
  int get hashCode => sparePartId.hashCode;

  @override
  String toString() {
    return 'PackageItemEntity(sparePartId: $sparePartId, name: $name, quantity: $quantity, unitPrice: $unitPrice, totalPrice: $totalPrice)';
  }
}
