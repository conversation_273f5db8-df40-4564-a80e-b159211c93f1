import '../../../../core/constants/api_end_points.dart';
import '../../../../core/enums/package_status.dart';
import 'cylinder_entity.dart';
import 'package_item_entity.dart';

class PackageEntity {
  final String id;
  final String name;
  final String description;
  final String cylinderId;
  final CylinderEntity? cylinder; // Optional populated cylinder
  final List<PackageItemEntity> includedSpareParts;
  final double totalPrice;
  final double costPrice;
  final double discount;
  final String imageUrl;
  final int quantity;
  final int reserved;
  final int sold;
  final int minimumStockLevel;
  final PackageStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  PackageEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.cylinderId,
    this.cylinder,
    required this.includedSpareParts,
    required this.totalPrice,
    required this.costPrice,
    required this.discount,
    required this.imageUrl,
    required this.quantity,
    required this.reserved,
    required this.sold,
    required this.minimumStockLevel,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // Computed properties
  int get availableQuantity => quantity - reserved;
  int get currentPhysicalStock => quantity; // Current stock in warehouse
  int get totalStock =>
      quantity + sold; // Total stock ever received (current + sold)
  int get reservedQuantity => reserved; // Stock reserved for orders
  int get soldQuantity => sold; // Total units sold

  // Stock status
  bool get isLowStock => availableQuantity <= minimumStockLevel;
  bool get isOutOfStock => availableQuantity <= 0;
  bool get hasReservedStock => reserved > 0;
  bool get hasSoldItems => sold > 0;

  // Pricing calculations
  double get finalPrice => totalPrice - discount;
  double get profitMargin => finalPrice - costPrice;
  double get profitPercentage =>
      costPrice > 0 ? (profitMargin / costPrice) * 100 : 0;
  double get discountPercentage =>
      totalPrice > 0 ? (discount / totalPrice) * 100 : 0;

  // Financial calculations
  double get totalRevenue => sold * finalPrice;
  double get totalCostAmount => sold * costPrice;
  double get totalProfit => totalRevenue - totalCostAmount;

  // Helper methods
  bool canReserve(int requestedQuantity) {
    return availableQuantity >= requestedQuantity;
  }

  String get formattedImageUrl => '${ApiEndpoints.host}$imageUrl';

  PackageEntity copyWith({
    String? id,
    String? name,
    String? description,
    String? cylinderId,
    CylinderEntity? cylinder,
    List<PackageItemEntity>? includedSpareParts,
    double? totalPrice,
    double? costPrice,
    double? discount,
    String? imageUrl,
    int? quantity,
    int? reserved,
    int? sold,
    int? minimumStockLevel,
    PackageStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PackageEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      cylinderId: cylinderId ?? this.cylinderId,
      cylinder: cylinder ?? this.cylinder,
      includedSpareParts: includedSpareParts ?? this.includedSpareParts,
      totalPrice: totalPrice ?? this.totalPrice,
      costPrice: costPrice ?? this.costPrice,
      discount: discount ?? this.discount,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      reserved: reserved ?? this.reserved,
      sold: sold ?? this.sold,
      minimumStockLevel: minimumStockLevel ?? this.minimumStockLevel,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PackageEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PackageEntity(id: $id, name: $name, totalPrice: $totalPrice, quantity: $quantity, status: $status)';
  }
}
