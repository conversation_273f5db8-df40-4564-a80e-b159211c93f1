import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import 'snack_bar_helper.dart';

class UrlLauncherHelper {
  /// Launch a phone call
  Future<void> launchTel({
    required BuildContext context,
    required String phoneNumber,
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'tel:$formatted';
    await _launchUrl(context: context, url: url);
  }

  /// Launch an email
  Future<void> launchEmail({
    required BuildContext context,
    required String toEmail,
    String? subject,
    String? body,
  }) async {
    final url = Uri(
      scheme: 'mailto',
      path: toEmail,
      query: _encodeQueryParams({
        if (subject != null) 'subject': subject,
        if (body != null) 'body': body,
      }),
    ).toString();
    await _launchUrl(context: context, url: url);
  }

  /// Launch WhatsApp chat
  Future<void> launchWhatsApp({
    required BuildContext context,
    required String phoneNumber,
    String message = '',
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'https://wa.me/$formatted?text=${Uri.encodeComponent(message)}';
    await _launchUrl(context: context, url: url);
  }

  /// Format Somali phone numbers to E.164 style: +252XXXXXXXXX
  String _formatPhoneNumber(String input) {
    // Remove all non-digit characters, but keep +
    final String cleaned = input.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleaned.startsWith('+252')) {
      return cleaned; // Already in correct format
    } else if (cleaned.startsWith('252')) {
      return '+$cleaned';
    } else if (cleaned.startsWith('0')) {
      // Replace leading 0 with +252
      return '+252${cleaned.substring(1)}';
    } else if (cleaned.startsWith('+')) {
      // Assume user added another country code, which is not Somalia
      // You could throw or sanitize here depending on your app logic
      return cleaned;
    } else {
      // Assume it's local number without prefix
      return '+252$cleaned';
    }
  }

  /// Encode query parameters for mailto links
  String _encodeQueryParams(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }

  /// Launch any URL and handle errors
  Future<void> _launchUrl({
    required BuildContext context,
    required String url,
  }) async {
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        final launched = await launchUrl(uri);
        if (!launched) {
          _showError(context, 'Could not launch URL');
        }
      } else {
        _showError(context, 'This action is not supported on your device.');
      }
    } catch (e) {
      _showError(context, 'Error launching URL: $e');
    }
  }

  void _showError(BuildContext context, String message) {
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }
}
