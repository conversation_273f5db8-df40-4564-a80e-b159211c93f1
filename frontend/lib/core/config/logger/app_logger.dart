import 'dart:async';
import 'package:logger/logger.dart';

class AppLogger {
  // Singleton pattern for the logger
  static final AppLogger _singleton = AppLogger._internal();

  factory AppLogger() {
    return _singleton;
  }

  static final isProduction = const bool.fromEnvironment('dart.vm.product');

  late Logger _logger;

  AppLogger._internal();

  // Set up the logger with different configurations for dev and production
  Future<void> setupLogging() async {
    // Define the log level based on the environment
    final level = isProduction ? Level.error : Level.all;

    // Create a logger with a custom printer
    _logger = Logger(printer: CustomerPrinter('AppLogger'), level: level);

    // // Optionally, add additional outputs (e.g., file logging)
    // if (!isProduction) {
    //   // Add console output for development
    //   _logger.addOutput(ConsoleOutput());
    // } else {
    //   // Add file output for production (example)
    //   // _logger.addOutput(FileOutput('app_logs.txt'));
    // }

    // Log a message to confirm the setup
    _logger.i(
      'Logging setup complete. Environment: ${isProduction ? 'Production' : 'Development'}',
    );
  }

  // Log methods for convenience
  void info(String message) => _logger.i(message);
  void warning(String message) => _logger.w(message);
  void error(String message, {Object? error, StackTrace? stackTrace}) =>
      _logger.e(message, error: error, stackTrace: stackTrace);
}

class CustomerPrinter extends LogPrinter {
  final String className;

  CustomerPrinter(this.className);

  // Define custom emojis for each log level
  static final Map<Level, String> levelEmojis = {
    Level.trace: '🔍', // Magnifying glass
    Level.debug: '🐛', // Bug
    Level.info: '💡', // Light bulb
    Level.warning: '⚠️', // Warning sign
    Level.error: '❌', // Cross mark
    Level.fatal: '💀', // Skull
    Level.off: '🔇', // Muted speaker
  };

  @override
  List<String> log(LogEvent event) {
    final prettyPrinter = PrettyPrinter(
      // colors: true,
      dateTimeFormat: DateTimeFormat.dateAndTime,
      // printEmojis: true,
    );
    final color =
        prettyPrinter.levelColors?[event.level] ?? const AnsiColor.none();
    final emoji =
        prettyPrinter.levelEmojis?[event.level] ?? levelEmojis[event.level]!;
    final message = event.message;

    return [color('$emoji $className: $message')];
  }
}

// // Example of a custom output (e.g., file logging)
// class FileOutput extends LogOutput {
//   final String filePath;

//   FileOutput(this.filePath);

//   @override
//   void output(OutputEvent event) {
//     // Write logs to a file (example implementation)
//     // You can use the `dart:io` library to write to a file.
//     // For simplicity, this is just a placeholder.
//     event.lines.forEach((line) {
//       print('Writing to file ($filePath): $line');
//     });
//   }
// }

// // Example of a console output
// class ConsoleOutput extends LogOutput {
//   @override
//   void output(OutputEvent event) {
//     // Print logs to the console
//     event.lines.forEach(print);
//   }
// }
