import 'dart:convert';
import 'package:http/http.dart' as http;

class AdminCommunicationService {
  static const String baseUrl = 'http://localhost:3010/api/v1';
  
  // SMS Service Methods
  
  /// Send SMS to individual user
  static Future<Map<String, dynamic>> sendIndividualSms({
    required String phoneNumber,
    required String message,
    String? senderId,
    String? refId,
    bool isOtp = false,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sms/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'phoneNumber': phoneNumber,
          'message': message,
          'senderId': senderId,
          'refId': refId,
          'isOtp': isOtp,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send SMS: $e');
    }
  }

  /// Send bulk SMS to multiple recipients
  static Future<Map<String, dynamic>> sendBulkSms({
    required List<String> phoneNumbers,
    required String message,
    String? senderId,
    String? refId,
    bool isOtp = false,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sms/send-bulk'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'phoneNumbers': phoneNumbers,
          'message': message,
          'senderId': senderId,
          'refId': refId,
          'isOtp': isOtp,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send bulk SMS: $e');
    }
  }

  /// Send SMS to users by role
  static Future<Map<String, dynamic>> sendSmsToRole({
    required String role,
    required String message,
    String? senderId,
    String? refId,
    bool isOtp = false,
    bool onlyActiveUsers = true,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sms/send-to-role'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'role': role,
          'message': message,
          'senderId': senderId,
          'refId': refId,
          'isOtp': isOtp,
          'onlyActiveUsers': onlyActiveUsers,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send role-based SMS: $e');
    }
  }

  /// Send OTP SMS
  static Future<Map<String, dynamic>> sendOtpSms({
    required String phoneNumber,
    required String otp,
    String? customMessage,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sms/send-otp'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'phoneNumber': phoneNumber,
          'otp': otp,
          'customMessage': customMessage,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send OTP SMS: $e');
    }
  }

  /// Get SMS service health status
  static Future<Map<String, dynamic>> getSmsHealth() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/sms/health'),
        headers: {
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to get SMS health: $e');
    }
  }

  /// Get SMS service metrics
  static Future<Map<String, dynamic>> getSmsMetrics() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/sms/metrics'),
        headers: {
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to get SMS metrics: $e');
    }
  }

  // Notification Service Methods
  
  /// Send notification to individual user
  static Future<Map<String, dynamic>> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notifications/send-to-user'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'userId': userId,
          'title': title,
          'body': body,
          'data': data,
          'imageUrl': imageUrl,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send notification to user: $e');
    }
  }

  /// Send notification to topic subscribers
  static Future<Map<String, dynamic>> sendNotificationToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
    bool onlyActiveUsers = true,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notifications/send-to-topic'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'topic': topic,
          'title': title,
          'body': body,
          'data': data,
          'imageUrl': imageUrl,
          'onlyActiveUsers': onlyActiveUsers,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to send notification to topic: $e');
    }
  }

  /// Get notification statistics
  static Future<Map<String, dynamic>> getNotificationStats({
    String? startDate,
    String? endDate,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final uri = Uri.parse('$baseUrl/notifications/stats').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to get notification stats: $e');
    }
  }

  /// Get available notification topics
  static Future<Map<String, dynamic>> getNotificationTopics() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications/topics'),
        headers: {
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to get notification topics: $e');
    }
  }

  /// Get notification history for current user
  static Future<Map<String, dynamic>> getNotificationHistory({
    int page = 1,
    int limit = 20,
    String? status,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      if (status != null) queryParams['status'] = status;

      final uri = Uri.parse('$baseUrl/notifications/history').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to get notification history: $e');
    }
  }

  /// Update FCM token for current user
  static Future<Map<String, dynamic>> updateFcmToken(String token) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/fcm-token'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'token': token,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to update FCM token: $e');
    }
  }

  /// Subscribe to notification topic
  static Future<Map<String, dynamic>> subscribeToTopic(String topic) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notifications/subscribe'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'topic': topic,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to subscribe to topic: $e');
    }
  }

  /// Toggle notification settings
  static Future<Map<String, dynamic>> toggleNotifications(bool enabled) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/toggle'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_getAuthToken()}',
        },
        body: jsonEncode({
          'enabled': enabled,
        }),
      );

      return _handleResponse(response);
    } catch (e) {
      throw Exception('Failed to toggle notifications: $e');
    }
  }

  // Helper Methods
  
  static Map<String, dynamic> _handleResponse(http.Response response) {
    final Map<String, dynamic> data = jsonDecode(response.body);
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw Exception(data['message'] ?? 'Request failed with status ${response.statusCode}');
    }
  }

  static String _getAuthToken() {
    // TODO: Implement actual token retrieval from secure storage
    // This should get the JWT token from your authentication service
    return 'your-jwt-token-here';
  }
}
