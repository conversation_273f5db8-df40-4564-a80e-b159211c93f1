import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:frontend/core/config/di/dependency_injection.dart';
import 'package:frontend/core/config/logger/app_logger.dart';
import 'package:frontend/core/services/notification_services.dart';

import 'app.dart';
import 'core/config/di/dependency_injection.dart' as di;
import 'core/config/enviroment/enviroment_config.dart';
import 'features/shared/presentation/pages/error_page.dart';
import 'firebase_options.dart';

// https://github.com/Abdiaziz2526/aamin_data_app.git

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Handle errors gracefully
  ErrorWidget.builder = (FlutterErrorDetails details) =>
      ModernErrorPage(details: details);

  /// setup logger
  final AppLogger appLogger = AppLogger();
  await appLogger.setupLogging();

  await Future.wait([
    // /// Load environment config
    EnvironmentConfig.loadEnv(),
    // /// Setup Dependency Injection
    di.setupServiceLocator(),
  ]);

  final notificationService = sl<NotificationService>();
  await notificationService.initialize();
  await notificationService.requestPermissions();
  final token = await notificationService.getToken();
  if (token != null) {
    AppLogger().info('FCM Token: $token');
  }

  ///
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const MyApp());
}

/*

# Check recent commits
git log --oneline -10

# Revert to a specific commit (keeps changes in working directory)
git reset --soft COMMIT_HASH

# Revert to a specific commit (discards all changes)
git reset --hard COMMIT_HASH

# Revert the last commit
git reset --hard HEAD~1

# Create a new commit that undoes previous changes
git revert COMMIT_HASH

*/
