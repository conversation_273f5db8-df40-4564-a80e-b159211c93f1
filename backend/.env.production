NODE_ENV = production
PORT = 3010
DATABASE_URL = "mongodb+srv://axmednajaad:<EMAIL>/gas_system_db?retryWrites=true&w=majority&appName=ecommerceWebAppCluster"
JWT_SECRET = "your_jwt_secret_key"
SALT_ROUND = 10
QR_SECRET_KEY = "your_qr_secret_key"
DISABLE_OTP_VERIFICATION = true
GOOGLE_MAP_API_KEY = "your_google_map_api_key"
SERVER_URL = "https://gas-booking-system-production.up.railway.app"
# CORS Configuration
FRONTEND_URL = "https://gas-booking-system-production.up.railway.app"
CORS_ORIGINS = "https://gas-booking-system-production.up.railway.app,https://admin.gas-booking-system-production.up.railway.app"
FIREBASE_PROJECT_ID = "ciribey-system-project"
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL = "<EMAIL>"
SMS_USERNAME = "Hodanhospital"
SMS_PASSWORD = "fj8TVv9w9eLUyknMUhyQpQ=="
SMS_PROVIDER_URL = "https://smsapi.hormuud.com"
SMS_SENDER_ID = "Ciribeey"
SMS_TIMEOUT = 30000
RASIIN_MERCHANT_UID = "M0913615"
RASIIN_MERCHANT_API_USER_ID = "1007359"
RASIIN_MERCHANT_API_KEY = "API-1221796037AHX"
RASIIN_MERCHANT_API_URL = https://api.waafipay.net/asm
