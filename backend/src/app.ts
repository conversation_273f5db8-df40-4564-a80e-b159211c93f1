import express, { Request, Response, NextFunction } from 'express';
import { json, urlencoded } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import { globalErrorHandler } from './middleware/error_handler_middle_ware';
import { config } from './config/env_config';
import { corsOptions, logCorsConfig } from './config/cors_config';
import { sendResponse } from './utils/response';
import appRouter from './routes';
import { NotFoundError } from './errors/app_errors';
import { loggerInterceptor } from './middleware/logger_interceptor_middleware';

const app = express();

// Log CORS configuration on startup
logCorsConfig();

// middleware
app.use(json());
app.use(urlencoded({ extended: true }));

// security
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' }, // Allow cross-origin requests for images
  })
);

// CORS - Configuration imported from config/cors_config.ts
// The CORS middleware automatically handles preflight OPTIONS requests
app.use(cors(corsOptions));

/// Logger Interceptor for request and response
// app.use(loggerInterceptor);

// routes
app.use(appRouter);

// test api
app.get('/', (_req: Request, res: Response) => {
  sendResponse(res, 200, 'success', 'Welcome to Gas System Project Backend');
  return;
});

// /health
app.get('/health', (_req: Request, res: Response) => {
  sendResponse(res, 200, 'success', 'Service is up and running');
  return;
});

// CORS test endpoint
app.get('/cors-test', (_req: Request, res: Response) => {
  const corsTestData = {
    message: 'CORS is working correctly!',
    timestamp: new Date().toISOString(),
    environment: config.server.env,
    corsConfig: {
      allowedOrigins: [...config.server.corsOrigins, config.server.frontendUrl, config.server.url],
      credentials: corsOptions.credentials,
      methods: corsOptions.methods,
    },
  };

  res.status(200).json({
    status: 'success',
    data: corsTestData,
  });
  return;
});

// 404 handler
app.use((req: Request, _res: Response, next: NextFunction) => {
  next(new NotFoundError(`Route not found: ${req.method} ${req.originalUrl}`));
});

// error handler
app.use(globalErrorHandler);

export default app;
