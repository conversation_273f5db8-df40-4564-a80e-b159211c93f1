import { CylinderType, SparePartCategory, SparePartStatus } from '../enums/enums';
import { Schema, model, Document } from 'mongoose';

export interface ISparePart extends Document {
  description?: string;
  price: number;
  cost: number; // Purchase cost for profit calculations
  stock: number;
  reserved: number;
  sold: number;
  status: SparePartStatus;
  category: SparePartCategory;
  compatibleCylinderTypes: CylinderType[];
  barcode?: string;
  location?: string;
  minimumStockLevel: number;
  lastRestockedAt?: Date;
  // Image Management - Simple path storage
  imageUrl?: string; // Legacy field for backward compatibility
  imagePath?: string; // New field for uploaded images (full path: uploads/images/spare-parts/...)
  createdAt: Date;
  updatedAt: Date;
  availableQuantity: number;

  // Soft delete fields
  isDeleted: boolean; // Soft delete flag
  deletedAt?: Date; // When it was deleted
  deletedBy?: string; // Who deleted it (user ID)
}

const SparePartSchema = new Schema<ISparePart>(
  {
    description: { type: String },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    stock: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    status: {
      type: String,
      enum: Object.values(SparePartStatus),
      default: SparePartStatus.AVAILABLE,
    },
    category: {
      type: String,
      enum: Object.values(SparePartCategory),
      required: true,
      unique: true, // Make category unique since it replaces name
    },
    compatibleCylinderTypes: [
      {
        type: String,
        enum: Object.values(CylinderType),
      },
    ],
    barcode: {
      type: String,
      // unique: true,
      // sparse: true,
    },
    minimumStockLevel: { type: Number, default: 5 },
    lastRestockedAt: { type: Date },
    imageUrl: { type: String }, // Legacy field
    imagePath: { type: String }, // New field for uploaded images

    // Soft delete fields
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
    deletedBy: { type: String },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
SparePartSchema.index({ status: 1 });
SparePartSchema.index({ stock: 1 });
// Note: category index is automatically created due to unique: true constraint
SparePartSchema.index({ compatibleCylinderTypes: 1 });
SparePartSchema.index(
  { category: 'text', description: 'text', barcode: 'text' },
  { weights: { category: 3, barcode: 2, description: 1 } }
);

// Soft delete indexes
SparePartSchema.index({ isDeleted: 1 }); // Filter deleted items
SparePartSchema.index({ isDeleted: 1, category: 1, status: 1 }); // Combined queries
SparePartSchema.index({ deletedAt: 1 }); // Sort by deletion date

// Virtuals
SparePartSchema.virtual('availableQuantity').get(function () {
  return Math.max(0, this.stock - this.reserved);
});

// Virtual for getting the correct image URL (prioritize uploaded images)
SparePartSchema.virtual('currentImageUrl').get(function (this: ISparePart) {
  if (this.imagePath) {
    // Remove 'uploads/' prefix for URL serving
    const urlPath = this.imagePath.replace(/^uploads\//, '');
    return `/api/v1/images/${urlPath}`;
  }
  return this.imageUrl; // Fallback to legacy imageUrl
});

SparePartSchema.path('price').validate(function (value) {
  return value >= this.cost;
}, 'Price must be >= cost');

export const SparePart = model<ISparePart>('SparePart', SparePartSchema);
