import crypto from 'crypto';

export interface IOtp {
  code: string;
  expirationTime: Date;
}

export function generateOtp(): IOtp {
  //   return crypto.randomInt(100000, 999999).toString();
  const otp = crypto.randomInt(100000, 999999).toString(); // Cryptographically secure
  const expirationTime = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes
  return { code: otp, expirationTime };

  // sample output
  // {
  //   code: '123456',
  //   expirationTime: 2025-06-18T14:29:27.417Z
  // }
}
