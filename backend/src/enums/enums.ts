// User Roles
export enum UserRole {
  CUSTOMER = 'customer',
  AGENT = 'agent',
  ADMIN = 'admin',
  SUPERVISOR = 'supervisor',
}

// Order Lifecycle
export enum OrderStatus {
  // Core Flow
  PENDING = 'PENDING', // Order created, awaiting payment
  CONFIRMED = 'CONFIRMED', // Payment successful
  IN_TRANSIT = 'IN_TRANSIT', // Driver dispatched
  DELIVERED = 'DELIVERED', // Successful completion

  // Termination States
  CANCELLED = 'CANCELLED', // User-initiated cancellation
  FAILED = 'FAILED', // System/payment failures
}

export enum EntityType {
  SparePart = 'SPARE_PART',
  Cylinder = 'CYLINDER',
  Package = 'PACKAGE',
}


export enum SparePartStatus {
  AVAILABLE = 'AVAILABLE',
  LOW_STOCK = 'LOW_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  DISCONTINUED = 'DISCONTINUED',
}

export enum SparePartCategory {
  BRASS_CONTROL_VALVE_KIT = 'BRASS_CONTROL_VALVE_KIT',
  REGULATOR_HIGH_PRESSURE = 'REGULATOR_HIGH_PRESSURE',
  REGULATOR_LOW_PRESSURE = 'REGULATOR_LOW_PRESSURE',
  SINGLE_BURNER_GAS_STOVE = 'SINGLE_BURNER_GAS_STOVE',
  THREE_BURNER_LPG_STOVE = 'THREE_BURNER_LPG_STOVE',
  TUBO_2_METER = 'TUBO_2_METER',
  VALUE_BURNER = 'VALUE_BURNER',
}

// Cylinder Types
export enum CylinderType {
  SixKg = '6KG',
  ThirteenKg = '13KG',
  SeventeenKg = '17KG',
  TwentyKg = '20KG',
  TwentyFiveKg = '25KG',
}

export enum CylinderStatus {
  Active = 'ACTIVE',
  Discontinued = 'DISCONTINUED',
  OutOfStock = 'OUT_OF_STOCK',
  
}

export enum CylinderMaterial {
  Metal = 'METAL',
  Plastic = 'PLASTIC',
}

export enum VehicleType {
  BIKE = 'bike',
  CAR = 'car',
  MOTORCYCLE = 'motorcycle',
}

// export enum Warehouse { // instead of `warehouse`
//   MAIN = 'ceelasha_warehouse',
//   SECONDARY = 'seybiyaano_warehouse',
//   THIRD = 'towfiiq_warehouse',
//   FOURTH = 'bakaaro_warehouse',
// }

// Payment Methods
export enum PaymentMethod {
  CASH = 'cash',
  WAAFI_PREAUTH = 'waafi_preauth',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PREAUTHORIZED = 'PREAUTHORIZED',
  CAPTURED = 'CAPTURED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

// Delivery Verification
export enum DeliveryVerificationMethod {
  QR_CODE = 'qr_code',
  OTP = 'otp',
  BIOMETRIC = 'biometric', // Future-proofing
}

export enum NotificationStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
}
