import { Request, Response, NextFunction } from 'express';
import { userService } from '../services/user.service';
import { sendResponse } from '../utils/response';
import { NotificationTopic } from '../utils/notification_utils';
import { UserRole } from '../enums/enums';
import { ValidationError } from '../errors/app_errors';

export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    const result = await userService.login(phone);
    sendResponse(res, 200, 'success', 'Login successful', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const registerAdminOrAgent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body;
    const result = await userService.registerAdminOrAgent(userData, req.user);
    sendResponse(res, 201, 'success', 'Admin/Agent registered successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const resendOtp = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    const result = await userService.resendOtp(phone);
    sendResponse(
      res,
      200,
      'success',
      'OTP resent successfully'
      //   {
      //   data: result,
      // }
    );
  } catch (error) {
    next(error);
  }
};

export const verifyOtp = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { phone, otp } = req.body;
    if (!phone || phone.trim() === '') {
      throw new ValidationError('Phone number is required');
    }
    if (!otp || otp.trim() === '') {
      throw new ValidationError('OTP is required');
    }
    const result = await userService.verifyOtp(phone, otp);
    sendResponse(res, 200, 'success', 'OTP verified successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getSingleUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const result = await userService.getSingleUser(userId, req.user);
    sendResponse(res, 200, 'success', 'User retrieved successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // const options = {
    //   role: req.query.role as UserRole,
    //   isActive: req.query.isActive === 'true',
    //   page: req.query.page ? parseInt(req.query.page as string) : undefined,
    //   limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
    //   sortBy: req.query.sortBy as string,
    //   sortOrder: req.query.sortOrder as 'asc' | 'desc',
    // };
    const options: any = {};

    if (req.query.role) {
      options.role = req.query.role as UserRole;
    }
    if (req.query.isActive !== undefined) {
      options.isActive = req.query.isActive === 'true';
    }
    if (req.query.page) {
      options.page = parseInt(req.query.page as string);
    }
    if (req.query.limit) {
      options.limit = parseInt(req.query.limit as string);
    }
    if (req.query.sortBy) {
      options.sortBy = req.query.sortBy as string;
    }
    if (req.query.sortOrder) {
      options.sortOrder = req.query.sortOrder as 'asc' | 'desc';
    }

    const result = await userService.getAllUsers(req.user, options);
    sendResponse(res, 200, 'success', 'Users retrieved successfully', {
      data: result.users,
      meta: result.pagination,
    });
  } catch (error) {
    next(error);
  }
};

export const updateUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const updateData = req.body;
    const result = await userService.updateUser(userId, updateData, req.user);
    sendResponse(res, 200, 'success', 'User updated successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const result = await userService.deleteUser(userId, req.user);
    sendResponse(res, 200, 'success', 'User deleted successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const subscribeToNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId, topic, deviceToken } = req.body;
    const result = await userService.subscribeToTopic(
      userId,
      topic as NotificationTopic,
      deviceToken,
      req.user
    );
    sendResponse(res, 200, 'success', 'Subscribed to notifications successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const result = await userService.getSingleUser(req.user.userId.toString(), req.user);
    sendResponse(res, 200, 'success', 'Current user retrieved successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const toggleUserActiveStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const isActive = req.body.isActive;
    const result = await userService.toggleUserActiveStatus(userId, isActive, req.user);
    sendResponse(res, 200, 'success', 'User active status toggled successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const toggleAgentOnDutyStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const isOnDuty = req.body.isOnDuty;
    const result = await userService.toggleAgentOnDutyStatus(userId, isOnDuty, req.user);
    sendResponse(res, 200, 'success', 'Agent on duty status toggled successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

export const changeUserRole = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.params.id;
    const { newRole } = req.body;

    if (!newRole || !Object.values(UserRole).includes(newRole)) {
      throw new ValidationError('Valid role is required');
    }

    const result = await userService.changeUserRole(userId, newRole, req.user);
    sendResponse(res, 200, 'success', 'User role changed successfully', {
      data: result,
    });
  } catch (error) {
    next(error);
  }
};
