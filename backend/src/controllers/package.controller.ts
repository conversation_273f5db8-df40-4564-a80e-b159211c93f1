import { Request, Response, NextFunction } from 'express';
import { packageService } from '../services/package.services';
import { sendResponse } from '../utils/response';
import { BadRequestError, ValidationError } from '../errors/app_errors';
import { EntityType } from '../enums/enums';
import { Types } from 'mongoose';
import logger from '../config/logger';
import { ImageUploadService } from '../services/image-upload.service';

class PackageController {
  /**
   * @route   POST /api/v1/packages
   * @desc    Create a new package
   * @access  Private (Admin only)
   */
  async createPackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        name,
        description,
        cylinder,
        includedSpareParts,
        totalPrice,
        costPrice,
        discount,
        imageUrl,
        quantity,
        minimumStockLevel,
      } = req.body;

      // Handle uploaded image file (if present)
      const imageFile = (req as any).file;
      let imagePath: string | undefined;

      if (imageFile) {
        // Process uploaded image
        imagePath = await ImageUploadService.processUploadedFile(imageFile, 'packages');
        logger.info(`Image uploaded for package: ${imagePath}`);
      }

      // Validation
      if (!name || !cylinder || !includedSpareParts) {
        throw new ValidationError('Name, cylinder, and included spare parts are required');
      }

      if (!Types.ObjectId.isValid(cylinder)) {
        throw new ValidationError('Invalid cylinder ID');
      }

      if (!Array.isArray(includedSpareParts) || includedSpareParts.length === 0) {
        throw new ValidationError('At least one spare part must be included');
      }

      // Validate spare parts structure
      for (const item of includedSpareParts) {
        if (!item.part || !Types.ObjectId.isValid(item.part)) {
          throw new ValidationError('Invalid spare part ID in included parts');
        }
        if (!item.quantity || item.quantity <= 0) {
          throw new ValidationError('Spare part quantity must be positive');
        }
      }

      if (totalPrice !== undefined && totalPrice <= 0) {
        throw new ValidationError('Total price must be positive');
      }

      if (costPrice !== undefined && costPrice <= 0) {
        throw new ValidationError('Cost price must be positive');
      }

      if (discount !== undefined && (discount < 0 || discount > 100)) {
        throw new ValidationError('Discount must be between 0 and 100');
      }

      const packageData = {
        name: name.trim(),
        description: description?.trim(),
        cylinder: new Types.ObjectId(cylinder),
        includedSpareParts,
        totalPrice: totalPrice ? Number(totalPrice) : undefined,
        costPrice: costPrice ? Number(costPrice) : undefined,
        discount: discount ? Number(discount) : undefined,
        imageUrl: imageUrl?.trim(), // Legacy field for backward compatibility
        imagePath, // New field for uploaded images
        quantity: quantity ? Number(quantity) : undefined,
        minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
      };

      const newPackage = await packageService.createPackage(packageData);

      logger.info('Package created successfully', {
        packageId: newPackage._id,
        name: newPackage.name,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 201, 'success', 'Package created successfully', {
        data: newPackage,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages
   * @desc    Get all packages with filtering and pagination
   * @access  Private (All authenticated users)
   */
  async getPackages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        search,
        cylinder,
        isActive,
        minPrice,
        maxPrice,
        page = 1,
        limit = 10,
        populate = 'true',
      } = req.query;

      // Validation
      const pageNum = Math.max(1, parseInt(page as string) || 1);
      const limitNum = Math.min(50, Math.max(1, parseInt(limit as string) || 10));
      const shouldPopulate = populate === 'true';

      const filters: any = {};
      if (search) filters.search = search as string;
      if (cylinder && Types.ObjectId.isValid(cylinder as string)) {
        filters.cylinder = new Types.ObjectId(cylinder as string);
      }
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (minPrice) filters.minPrice = Number(minPrice);
      if (maxPrice) filters.maxPrice = Number(maxPrice);

      const result = await packageService.listPackages(
        filters,
        { page: pageNum, limit: limitNum },
        shouldPopulate
      );

      sendResponse(res, 200, 'success', 'Packages retrieved successfully', {
        data: result.data,
        meta: {
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: result.total,
            pages: Math.ceil(result.total / limitNum),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/:id
   * @desc    Get package by ID
   * @access  Private (All authenticated users)
   */
  async getPackageById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { populate = 'true' } = req.query;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const shouldPopulate = populate === 'true';
      const packageDoc = await packageService.getPackageById(
        new Types.ObjectId(id),
        shouldPopulate
      );

      sendResponse(res, 200, 'success', 'Package retrieved successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/packages/:id
   * @desc    Update package details
   * @access  Private (Admin only)
   */
  async updatePackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Handle uploaded image file (if present)
      const imageFile = (req as any).file;
      let imagePath: string | undefined;

      if (imageFile) {
        // Process uploaded image
        imagePath = await ImageUploadService.processUploadedFile(imageFile, 'packages');
        logger.info(`Image uploaded for package update: ${imagePath}`);
        updateData.imagePath = imagePath;
      }

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      // Validate numeric fields if provided
      if (updateData.totalPrice !== undefined && updateData.totalPrice <= 0) {
        throw new ValidationError('Total price must be positive');
      }
      if (updateData.costPrice !== undefined && updateData.costPrice <= 0) {
        throw new ValidationError('Cost price must be positive');
      }
      if (
        updateData.discount !== undefined &&
        (updateData.discount < 0 || updateData.discount > 100)
      ) {
        throw new ValidationError('Discount must be between 0 and 100');
      }

      // Validate spare parts if provided
      if (updateData.includedSpareParts) {
        if (
          !Array.isArray(updateData.includedSpareParts) ||
          updateData.includedSpareParts.length === 0
        ) {
          throw new ValidationError('At least one spare part must be included');
        }

        for (const item of updateData.includedSpareParts) {
          if (!item.part || !Types.ObjectId.isValid(item.part)) {
            throw new ValidationError('Invalid spare part ID in included parts');
          }
          if (!item.quantity || item.quantity <= 0) {
            throw new ValidationError('Spare part quantity must be positive');
          }
        }
      }

      const packageDoc = await packageService.updatePackage(new Types.ObjectId(id), updateData);

      logger.info('Package updated successfully', {
        packageId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package updated successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/packages/:id/toggle-status
   * @desc    Toggle package active status
   * @access  Private (Admin only)
   */
  async togglePackageStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const packageDoc = await packageService.togglePackageStatus(new Types.ObjectId(id));

      logger.info('Package status toggled successfully', {
        packageId: id,
        newStatus: packageDoc.isActive,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package status updated successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/analytics
   * @desc    Get package analytics
   * @access  Private (Admin only)
   */
  async getPackageAnalytics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const analytics = await packageService.getPackageAnalytics();

      sendResponse(res, 200, 'success', 'Package analytics retrieved successfully', {
        data: analytics,
      });
    } catch (error) {
      next(error);
    }
  }

  // ==================== ADMINISTRATIVE INVENTORY METHODS ====================
  // Note: Reservation, release, and sales operations are now handled automatically
  // through the order lifecycle. Only administrative operations remain.

  /**
   * @route   POST /api/v1/packages/:id/restock
   * @desc    Restock package
   * @access  Private (Admin only)
   */
  async restockPackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity } = req.body;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      if (!quantity || quantity <= 0) {
        throw new ValidationError('Quantity must be a positive number');
      }

      const packageDoc = await packageService.restockPackage(id, Number(quantity));

      logger.info('Package restocked successfully', {
        packageId: id,
        quantity: Number(quantity),
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package restocked successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/packages/:id/adjust-stock
   * @desc    Adjust package stock
   * @access  Private (Admin only)
   */
  async adjustPackageStock(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { adjustment, reason } = req.body;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      if (adjustment === undefined || adjustment === 0) {
        throw new ValidationError('Adjustment must be a non-zero number');
      }

      if (!reason || typeof reason !== 'string') {
        throw new ValidationError('Reason is required');
      }

      const packageDoc = await packageService.adjustPackageStock(
        id,
        Number(adjustment),
        reason.trim()
      );

      logger.info('Package stock adjusted successfully', {
        packageId: id,
        adjustment: Number(adjustment),
        reason: reason.trim(),
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package stock adjusted successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/low-stock
   * @desc    Get packages with low stock
   * @access  Private (Admin, Agent)
   */
  async getLowStockPackages(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const packages = await packageService.getLowStockPackages();

      sendResponse(res, 200, 'success', 'Low stock packages retrieved successfully', {
        data: packages,
        meta: { count: packages.length },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/:id/availability
   * @desc    Check package availability
   * @access  Private (All authenticated users)
   */
  async checkPackageAvailability(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { quantity = 1 } = req.query;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const requestedQuantity = Math.max(1, parseInt(quantity as string) || 1);
      const availability = await packageService.checkPackageAvailability(id, requestedQuantity);

      sendResponse(res, 200, 'success', 'Package availability checked successfully', {
        data: availability,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/:id/available-quantity
   * @desc    Get available package quantity
   * @access  Private (All authenticated users)
   */
  async getAvailableQuantity(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const availableQuantity = await packageService.getAvailablePackageQuantity(id);

      sendResponse(res, 200, 'success', 'Available quantity retrieved successfully', {
        data: { availableQuantity },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/sales-statistics
   * @desc    Get package sales statistics
   * @access  Private (Admin only)
   */
  async getPackageSalesStatistics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await packageService.getPackageSalesStatistics();

      sendResponse(res, 200, 'success', 'Package sales statistics retrieved successfully', {
        data: statistics,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   PUT /api/v1/packages/bulk-status
   * @desc    Bulk update package statuses
   * @access  Private (Admin only)
   */
  async bulkUpdatePackageStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { packageIds, isActive } = req.body;

      if (!Array.isArray(packageIds) || packageIds.length === 0) {
        throw new ValidationError('Package IDs array is required');
      }

      if (typeof isActive !== 'boolean') {
        throw new ValidationError('isActive must be a boolean');
      }

      // Validate all package IDs
      for (const id of packageIds) {
        if (!Types.ObjectId.isValid(id)) {
          throw new ValidationError(`Invalid package ID: ${id}`);
        }
      }

      const modifiedCount = await packageService.bulkUpdatePackageStatus(packageIds, isActive);

      logger.info('Bulk package status update completed', {
        packageIds,
        isActive,
        modifiedCount,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package statuses updated successfully', {
        data: { modifiedCount },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   DELETE /api/v1/packages/:id
   * @desc    Soft delete a package
   * @access  Private (Admin only)
   */
  async deletePackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const packageDoc = await packageService.deletePackage(id, req.user?.userId.toString());

      logger.info('Package soft deleted successfully', {
        packageId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package deleted successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   POST /api/v1/packages/:id/restore
   * @desc    Restore a soft-deleted package
   * @access  Private (Admin only)
   */
  async restorePackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const packageDoc = await packageService.restorePackage(id);

      logger.info('Package restored successfully', {
        packageId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package restored successfully', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   DELETE /api/v1/packages/:id/permanent
   * @desc    Permanently delete a package (hard delete)
   * @access  Private (Admin only)
   */
  async permanentlyDeletePackage(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestError('Invalid package ID');
      }

      const packageDoc = await packageService.permanentlyDeletePackage(id);

      logger.info('Package permanently deleted', {
        packageId: id,
        userId: req.user?.userId,
        timestamp: new Date().toISOString(),
      });

      sendResponse(res, 200, 'success', 'Package permanently deleted', {
        data: packageDoc,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @route   GET /api/v1/packages/deleted
   * @desc    Get soft-deleted packages
   * @access  Private (Admin only)
   */
  async getDeletedPackages(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await packageService.listDeletedPackages({ page, limit });

      sendResponse(res, 200, 'success', 'Deleted packages retrieved successfully', {
        data: result.data,
        meta: {
          pagination: {
            page,
            limit,
            total: result.total,
            pages: Math.ceil(result.total / limit),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export const packageController = new PackageController();
