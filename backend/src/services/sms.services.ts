import axios, { AxiosInstance } from 'axios';
import qs from 'querystring';
import { config } from '../config/env_config';
import { HormuudSmsError, HormuudServiceError } from '../errors/app_errors';
import logger from '../config/logger';

class SmsService {
  private static instance: SmsService;
  private token: string | null = null;
  private tokenExpiry: number | null = null;
  private lastRequestTime = 0;
  private readonly axiosInstance: AxiosInstance;
  private readonly minRequestDelay = 100; // 100ms between requests
  private metrics = {
    sentCount: 0,
    failedCount: 0,
    lastError: null as Error | null,
  };
  private activeRequests = 0;
  private readonly maxConcurrent = 5; // Adjust based on provider limits
  private readonly retryConfig = {
    maxRetries: 3,
    retryDelay: 1000, // 1 second
  };

  private async concurrencyLimit<T>(fn: () => Promise<T>): Promise<T> {
    while (this.activeRequests >= this.maxConcurrent) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    this.activeRequests++;
    try {
      return await fn();
    } finally {
      this.activeRequests--;
    }
  }

  private constructor() {
    this.validateConfig();
    this.axiosInstance = axios.create({
      // timeout: 10000,
      timeout: config.sms.timeout,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  public static getInstance(): SmsService {
    if (!SmsService.instance) {
      SmsService.instance = new SmsService();
    }
    return SmsService.instance;
  }

  public getMetrics() {
    return {
      ...this.metrics,
      successRate:
        this.metrics.sentCount > 0
          ? (this.metrics.sentCount - this.metrics.failedCount) / this.metrics.sentCount
          : 1,
    };
  }

  public async checkHealth(): Promise<boolean> {
    try {
      await this.getToken(true); // Force new token check
      return true;
    } catch (error) {
      this.log('Health check failed', 'error', error);
      return false;
    }
  }

  public async sendSms(
    phoneNumber: string,
    message: string,
    options?: {
      isOtp?: boolean;
      senderId?: string;
      refId?: string;
    }
  ): Promise<{ messageId: string }> {
    try {
      await this.rateLimit();

      // Validate inputs
      this.validatePhoneNumber(phoneNumber);
      this.validateMessage(message, options?.isOtp);

      const token = await this.getToken();
      const url = `${config.sms.providerUrl}/api/SendSMS`;
      const senderid = options?.senderId || config.sms.senderId;
      // const senderid = config.sms.senderId;

      const response = await this.axiosInstance.post(
        url,
        {
          mobile: phoneNumber,
          message: message,
          senderid,
          refid: options?.refId || Date.now().toString(),
          validity: 0,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.data.ResponseCode !== '200') {
        throw new HormuudSmsError(response.data.ResponseCode, response.data, {
          recipient: phoneNumber,
          messageContent: message,
          isOtp: options?.isOtp,
        });
      }

      this.metrics.sentCount++;
      this.log('SMS sent successfully', 'info', {
        recipient: phoneNumber,
        sender: senderid,
        messageId: response.data.Data?.MessageID,
      });

      return { messageId: response.data.Data?.MessageID };
    } catch (error) {
      this.metrics.failedCount++;
      this.metrics.lastError = error instanceof Error ? error : new Error(String(error));

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new HormuudServiceError('SMS request timed out', {
            isOtp: options?.isOtp,
            retryAfter: 60, // 1 minute
          });
        }

        if (error.response?.status === 401) {
          this.token = null;
          this.tokenExpiry = null;
        }
      }

      if (error instanceof HormuudSmsError || error instanceof HormuudServiceError) {
        throw error;
      }

      throw new HormuudServiceError('Unexpected SMS service error', {
        isOtp: options?.isOtp,
        details: this.safeErrorDetails(error),
      });
    }
  }

  // send sms to list of people
  public async sendSmsToMany(
    phoneNumbers: string[],
    message: string,
    options?: {
      isOtp?: boolean;
      senderId?: string;
      refId?: string;
      onProgress?: (success: number, failed: number, total: number) => void;
    }
  ): Promise<{
    status: 'success' | 'partial_success' | 'failed';
    message: string;
    data: {
      messageIds: {
        successes: { phoneNumber: string; messageId: string }[];
        failures: { phoneNumber: string; error: Error }[];
      };
      counts: {
        requested: number;
        duplicatesRemoved: number;
        attempted: number;
        successful: number;
        failed: number;
      };
      sentAt: string;
    };
  }> {
    // Clean and normalize numbers
    const cleanedNumbers = phoneNumbers.map(num => num.trim().replace(/\s+/g, ''));

    // Remove duplicates while preserving original order
    const uniqueNumbers: string[] = [];
    const duplicatesRemoved = new Set<string>();

    cleanedNumbers.forEach(num => {
      if (!uniqueNumbers.includes(num)) {
        uniqueNumbers.push(num);
      } else {
        duplicatesRemoved.add(num);
      }
    });

    // Validate message (common for all)
    this.validateMessage(message, options?.isOtp);

    const results = {
      successes: [] as { phoneNumber: string; messageId: string }[],
      failures: [] as { phoneNumber: string; error: Error }[],
    };

    const updateProgress = () => {
      options?.onProgress?.(
        results.successes.length,
        results.failures.length,
        uniqueNumbers.length
      );
    };

    const BATCH_SIZE = 100;

    for (let i = 0; i < uniqueNumbers.length; i += BATCH_SIZE) {
      const batch = uniqueNumbers.slice(i, i + BATCH_SIZE);

      await Promise.all(
        batch.map(phoneNumber =>
          this.concurrencyLimit(async () => {
            try {
              this.validatePhoneNumber(phoneNumber);
              await this.rateLimit();
              const { messageId } = await this.sendSms(phoneNumber, message, options);
              results.successes.push({ phoneNumber, messageId });
            } catch (error) {
              results.failures.push({
                phoneNumber,
                error: error instanceof Error ? error : new Error(String(error)),
              });
            }
            updateProgress();
          })
        )
      );
    }

    // Determine result status
    let status: 'success' | 'partial_success' | 'failed' = 'success';
    let responseMessage = 'Bulk SMS sent successfully';

    if (results.failures.length > 0) {
      status = results.successes.length > 0 ? 'partial_success' : 'failed';
      responseMessage =
        results.successes.length > 0
          ? 'Bulk SMS sent with some failures'
          : 'Bulk SMS failed for all recipients';
    }

    return {
      status,
      message: responseMessage,
      data: {
        messageIds: {
          successes: results.successes,
          failures: results.failures,
        },
        counts: {
          requested: phoneNumbers.length,
          duplicatesRemoved: duplicatesRemoved.size,
          attempted: uniqueNumbers.length,
          successful: results.successes.length,
          failed: results.failures.length,
        },
        sentAt: new Date().toISOString(),
      },
    };
  }

  private async getToken(forceRefresh = false): Promise<string> {
    // Return cached token if valid (with 5 minute buffer)
    if (!forceRefresh && this.token && this.tokenExpiry && Date.now() < this.tokenExpiry - 300000) {
      return this.token;
    }

    try {
      const url = `${config.sms.providerUrl}/token`;
      const payload = qs.stringify({
        username: config.sms.username,
        password: config.sms.password,
        grant_type: 'password',
      });

      const response = await axios.post(url, payload, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 5000,
      });

      if (!response.data.access_token) {
        throw new Error('No access token received');
      }

      // Cache token
      this.token = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in || 3600) * 1000;

      return this.token;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        throw new HormuudSmsError('201', error.response.data);
      }

      throw new HormuudServiceError('Failed to authenticate with SMS service', {
        details: this.safeErrorDetails(error),
      });
    }
  }

  private validateConfig() {
    const required = ['providerUrl', 'username', 'password', 'senderId'];
    const missing = required.filter(key => !config.sms[key as keyof typeof config.sms]);

    if (missing.length > 0) {
      throw new Error(`Missing SMS configuration: ${missing.join(', ')}`);
    }
  }

  private validatePhoneNumber(phoneNumber: string): void {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const regex = /^(252)?(61|62|65|66|67|68|69|71|77|79|88|89|90)\d{7}$/;

    if (!regex.test(cleaned)) {
      throw new HormuudSmsError(
        '207',
        {},
        {
          recipient: phoneNumber,
          details: 'Invalid Somalia phone number format',
        }
      );
    }
  }

  private validateMessage(message: string, isOtp = false): void {
    const maxLength = isOtp ? 160 : 459;
    if (message.length > maxLength) {
      throw new HormuudSmsError(
        '206',
        {},
        {
          isOtp,
          details: `Message too long (max ${maxLength} characters)`,
        }
      );
    }
  }

  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const delay = this.lastRequestTime + this.minRequestDelay - now;

    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  private log(message: string, level: 'info' | 'warn' | 'error' = 'info', data?: any) {
    // console.log(
    //   JSON.stringify({
    //     timestamp: new Date().toISOString(),
    //     service: 'sms',
    //     level,
    //     message,
    //     ...data,
    //   })
    // );
    logger.info(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        service: 'sms',
        level,
        message,
        ...data,
      })
    );
  }

  private safeErrorDetails(error: unknown): unknown {
    if (error instanceof Error) {
      return {
        message: error.message,
        stack: error.stack,
        // ...(error.cause && { cause: this.safeErrorDetails(error.cause) }),
      };
    }
    return String(error);
  }
}

export const smsService = SmsService.getInstance();
