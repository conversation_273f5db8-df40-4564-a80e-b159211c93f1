import { NotFoundError, BadRequestError, InternalServerError } from '../errors/app_errors';
import { User, Notification } from '../models';
import * as notificationUtil from '../utils/notification_utils';
import { NotificationTopic } from '../utils/notification_utils';
import logger from '../config/logger';

class NotificationService {
  /**
   * Send notification to a specific user
   */
  async sendToUser(
    userId: string,
    payload: {
      title: string;
      body: string;
      data?: Record<string, string>;
      imageUrl?: string;
    }
  ): Promise<notificationUtil.NotificationResult> {
    try {
      // Validate input
      if (!userId) throw new BadRequestError('User ID is required');
      if (!payload?.title || !payload?.body) {
        throw new BadRequestError('Title and body are required');
      }

      const user = await User.findById(userId).select('notification');
      if (!user) throw new NotFoundError('User not found');

      if (!user.notification) {
        throw new NotFoundError('Notification settings not configured');
      }

      if (!user.notification.isEnabled) {
        throw new BadRequestError('Notifications are disabled for this user');
      }

      if (!user.notification.fcmToken) {
        throw new BadRequestError('FCM token not registered for this user');
      }

      // Create notification record
      const notificationRecord = await Notification.create({
        userId,
        title: payload.title,
        body: payload.body,
        data: payload.data,
        imageUrl: payload.imageUrl,
        status: 'pending',
      });

      try {
        // Send the actual push notification
        const result = await notificationUtil.sendPushNotification(
          user.notification.fcmToken,
          payload
        );

        // Update notification status
        await Notification.findByIdAndUpdate(notificationRecord._id, {
          status: 'delivered',
          deliveredAt: new Date(),
          result,
        });

        logger.info(`Notification sent to user ${userId}`, {
          notificationId: notificationRecord._id,
          title: payload.title,
        });

        return {
          success: true,
          //   notificationId: notificationRecord._id,
          details: result,
        };
      } catch (error) {
        await Notification.findByIdAndUpdate(notificationRecord._id, {
          status: 'failed',
          error: error.message,
        });

        logger.error(`Failed to send notification to user ${userId}`, {
          error: error.message,
          notificationId: notificationRecord._id,
        });

        throw new InternalServerError('Failed to send notification');
      }
    } catch (error) {
      logger.error('NotificationService.sendToUser error', {
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Send notification to all users subscribed to a topic
   */
  async sendToTopic(
    topic: NotificationTopic,
    payload: {
      title: string;
      body: string;
      data?: Record<string, string>;
      imageUrl?: string;
    },
    options?: {
      onlyActiveUsers?: boolean;
    }
  ): Promise<notificationUtil.NotificationResult> {
    try {
      // Validate input
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      if (!payload?.title || !payload?.body) {
        throw new BadRequestError('Title and body are required');
      }

      // Get all users subscribed to this topic
      const query: any = {
        'notification.topics': topic,
        'notification.isEnabled': true,
      };

      if (options?.onlyActiveUsers) {
        query.isActive = true;
      }

      const users = await User.find(query).select('notification isActive').lean();

      if (!users.length) {
        logger.warn(`No users found subscribed to topic ${topic}`);
        return {
          success: true,
          message: 'No subscribed users found',
          count: 0,
        };
      }

      // Filter users with valid FCM tokens
      const validUsers = users.filter(u => u.notification?.fcmToken);
      const tokens = validUsers.map(u => u.notification.fcmToken);

      if (!tokens.length) {
        logger.warn(`No valid FCM tokens found for topic ${topic}`);
        return {
          success: true,
          message: 'No valid FCM tokens found',
          count: 0,
        };
      }

      // Create bulk notification records
      const notificationRecords = await Notification.insertMany(
        validUsers.map(user => ({
          userId: user._id,
          title: payload.title,
          body: payload.body,
          data: payload.data,
          imageUrl: payload.imageUrl,
          topic,
          status: 'pending',
        }))
      );

      try {
        // Send to topic
        const result = await notificationUtil.sendToTopic(topic, payload);

        // Update all notification records
        await Notification.updateMany(
          { _id: { $in: notificationRecords.map(n => n._id) } },
          {
            status: 'delivered',
            deliveredAt: new Date(),
            result,
          }
        );

        logger.info(`Notification sent to topic ${topic}`, {
          count: validUsers.length,
          title: payload.title,
        });

        return {
          success: true,
          count: validUsers.length,
          details: result,
        };
      } catch (error) {
        await Notification.updateMany(
          { _id: { $in: notificationRecords.map(n => n._id) } },
          {
            status: 'failed',
            error: error.message,
          }
        );

        logger.error(`Failed to send notification to topic ${topic}`, {
          error: error.message,
          count: validUsers.length,
        });

        throw new InternalServerError('Failed to send topic notification');
      }
    } catch (error) {
      logger.error('NotificationService.sendToTopic error', {
        topic,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Update user's FCM token
   */
  async updateFcmToken(userId: string, token: string): Promise<{ success: boolean }> {
    try {
      if (!userId || !token) {
        throw new BadRequestError('User ID and token are required');
      }

      const user = await User.findByIdAndUpdate(
        userId,
        {
          $set: {
            'notification.fcmToken': token,
            'notification.isEnabled': true,
          },
        },
        { new: true }
      );

      if (!user) throw new NotFoundError('User not found');

      logger.info(`Updated FCM token for user ${userId}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to update FCM token', {
        userId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Subscribe user to notification topic
   */
  async subscribeToTopic(userId: string, topic: NotificationTopic): Promise<{ success: boolean }> {
    try {
      if (!Object.values(NotificationTopic).includes(topic)) {
        throw new BadRequestError('Invalid notification topic');
      }

      const user = await User.findById(userId).select('notification');
      if (!user) throw new NotFoundError('User not found');

      if (!user.notification) {
        throw new BadRequestError('Notification settings not initialized');
      }

      // Check if already subscribed
      if (user.notification.topics?.includes(topic)) {
        return { success: true };
      }

      // Update in database
      await User.findByIdAndUpdate(userId, { $addToSet: { 'notification.topics': topic } });

      // Subscribe with FCM if token exists
      if (user.notification.fcmToken) {
        try {
          await notificationUtil.subscribeToTopic([user.notification.fcmToken], topic);
        } catch (error) {
          logger.error('FCM topic subscription failed', {
            userId,
            topic,
            error: error.message,
          });
          // Continue even if FCM fails - we'll sync later
        }
      }

      logger.info(`User ${userId} subscribed to topic ${topic}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to subscribe to topic', {
        userId,
        topic,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Toggle notification enable/disable
   */
  async toggleNotifications(userId: string, enabled: boolean): Promise<{ success: boolean }> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: { 'notification.isEnabled': enabled } },
        { new: true }
      );

      if (!user) throw new NotFoundError('User not found');

      logger.info(`Notifications ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);

      return { success: true };
    } catch (error) {
      logger.error('Failed to toggle notifications', {
        userId,
        enabled,
        error: error.message,
      });
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
