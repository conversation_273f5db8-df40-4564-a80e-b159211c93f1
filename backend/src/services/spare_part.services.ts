import mongoose, { ClientSession, Types } from 'mongoose';
import { SparePart, ISparePart } from '../models/spareParts.model';
import { SparePartCategory, SparePartStatus, CylinderType, UserRole } from '../enums/enums';
import { BadRequestError, NotFoundError, DuplicateResourceError } from '../errors/app_errors';
import { getSparePartImageUrl } from '../utils/image-url-generator';
import logger from '../config/logger';
import { FileCleanupService } from '../utils/file_cleanup';

class SparePartService {
  /**
   * Create a new spare part
   * @throws {DuplicateResourceError} If spare part with same category/barcode exists
   */
  async createSparePart(
    data: {
      description?: string;
      price: number;
      cost: number;
      category: SparePartCategory;
      compatibleCylinderTypes?: CylinderType[];
      barcode?: string;
      minimumStockLevel?: number;
      initialStock?: number;
      initialReserved?: number;
      initialSold?: number;
      imageUrl?: string;
      imagePath?: string;
    },
    session?: ClientSession
  ): Promise<ISparePart> {
    const sessionToUse = session || (await mongoose.startSession());
    const shouldCommit = !session; // Only commit if we started the session

    try {
      if (!session) sessionToUse.startTransaction();

      // Check for duplicates - category must be unique
      const existing = await SparePart.findOne({
        category: data.category,
      }).session(sessionToUse);

      if (existing) {
        throw new DuplicateResourceError('Spare part with this category already exists', {
          code: 'DUPLICATE_SPARE_PART',
        });
      }

      const initialStock = data.initialStock ?? 0;
      const initialReserved = data.initialReserved ?? 0;
      const initialSold = data.initialSold ?? 0;
      const minimumStockLevel = data.minimumStockLevel ?? 5;
      const dynamicImageUrl = getSparePartImageUrl(data.category);

      const sparePart = new SparePart({
        ...data,
        stock: initialStock,
        reserved: initialReserved,
        sold: initialSold,
        minimumStockLevel,
        status: this.determineStatus(initialStock, initialReserved, minimumStockLevel),
        imageUrl: dynamicImageUrl,
      });

      await sparePart.save({ session: sessionToUse });

      if (shouldCommit) {
        await sessionToUse.commitTransaction();
      }

      return sparePart;
    } catch (error) {
      if (shouldCommit) {
        await sessionToUse.abortTransaction();
      }
      throw error;
    } finally {
      if (shouldCommit) {
        sessionToUse.endSession();
      }
    }
  }

  /**
   * Update spare part details
   * @throws {NotFoundError} If spare part not found
   */
  async updateSparePart(
    id: string | Types.ObjectId,
    updateData: Partial<ISparePart>,
    session?: ClientSession
  ): Promise<ISparePart> {
    // Prevent updating inventory fields through this method
    const { stock, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;

    const sparePart = await SparePart.findByIdAndUpdate(id, safeUpdateData, {
      new: true,
      runValidators: true,
      session,
    });

    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    const restrictedFields = ['stock', 'reserved', 'sold', 'availableQuantity'];
    const attemptedRestrictedUpdates = Object.keys(updateData).filter(key =>
      restrictedFields.includes(key)
    );
    if (attemptedRestrictedUpdates.length > 0) {
      logger.warn('Restricted fields ignored in updateSparePart', {
        id,
        fields: attemptedRestrictedUpdates,
        userAttempt: updateData,
      });
    }

    // Recalculate status if minimumStockLevel changed
    if ('minimumStockLevel' in safeUpdateData) {
      sparePart.status = this.determineStatus(
        sparePart.stock,
        sparePart.reserved,
        sparePart.minimumStockLevel
      );
    }
    await sparePart.save({ session });

    return sparePart;
  }

  /**
   * Get spare part by ID (excludes soft-deleted items)
   * @throws {NotFoundError} If spare part not found
   */
  async getSparePartById(
    id: string | Types.ObjectId,
    includeDeleted: boolean = false
  ): Promise<ISparePart> {
    const query: any = { _id: id };
    if (!includeDeleted) {
      query.isDeleted = false;
    }

    const sparePart = await SparePart.findOne(query);
    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }
    return sparePart;
  }

  /**
   * List spare parts with filtering and pagination
   */
  async listSpareParts(
    filters: {
      search?: string;
      category?: SparePartCategory;
      status?: SparePartStatus;
      compatibleWith?: CylinderType;
      lowStock?: boolean;
      includeDeleted?: boolean;
    } = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 50 },
    requestedUser: { userId: string | Types.ObjectId; role: UserRole }
  ): Promise<{ data: ISparePart[]; total: number }> {
    const query: any = {};

    // Exclude soft-deleted items by default
    if (!filters.includeDeleted) {
      query.isDeleted = false;
    }

    // Text search
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    // Category filter
    if (filters.category) {
      query.category = filters.category;
    }

    // Status filter
    if (filters.status) {
      console.log('Status filter: ', {
        requestedUser,
        filters,
      });
      // query.status = filters.status;
      if (requestedUser.role === UserRole.CUSTOMER) {
        query.status = SparePartStatus.AVAILABLE;
      } else {
        query.status = filters.status;
      }
    } else {
      if (requestedUser.role === UserRole.CUSTOMER) {
        query.status = SparePartStatus.AVAILABLE;
      }
    }

    // Compatibility filter
    if (filters.compatibleWith) {
      query.compatibleCylinderTypes = filters.compatibleWith;
    }

    // Low stock filter
    if (filters.lowStock) {
      query.$expr = { $lte: ['$stock', '$minimumStockLevel'] };
    }

    const [data, total] = await Promise.all([
      SparePart.find(query)
        .sort({ name: 1 })
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit),
      SparePart.countDocuments(query),
    ]);

    return { data, total };
  }

  /**
   * Restock spare parts
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If quantity is invalid
   */
  async restock(
    id: string | Types.ObjectId,
    quantity: number,
    session?: ClientSession
  ): Promise<ISparePart> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const sparePart = await SparePart.findById(id).session(session || null);
    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    sparePart.stock += quantity;
    sparePart.lastRestockedAt = new Date();
    sparePart.status = this.determineStatus(
      sparePart.stock,
      sparePart.reserved,
      sparePart.minimumStockLevel
    );
    await sparePart.save({ session });

    return sparePart;
  }

  /**
   * Reserve spare parts for an order
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If insufficient stock
   */
  async reserve(
    id: string | Types.ObjectId,
    quantity: number,
    session?: ClientSession
  ): Promise<ISparePart> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const sparePart = await SparePart.findById(id).session(session || null);
    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    if (sparePart.availableQuantity < quantity) {
      throw new BadRequestError('Insufficient stock', {
        code: 'INSUFFICIENT_STOCK',
        details: {
          available: sparePart.availableQuantity,
          requested: quantity,
        },
      });
    }

    sparePart.reserved += quantity;
    sparePart.status = this.determineStatus(
      sparePart.stock,
      sparePart.reserved,
      sparePart.minimumStockLevel
    );
    await sparePart.save({ session });

    return sparePart;
  }

  /**
   * Release reserved spare parts
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If trying to release more than reserved
   */
  async release(
    id: string | Types.ObjectId,
    quantity: number,
    session?: ClientSession
  ): Promise<ISparePart> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const sparePart = await SparePart.findById(id).session(session || null);
    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    if (sparePart.reserved < quantity) {
      throw new BadRequestError('Cannot release more than reserved quantity', {
        code: 'INVALID_RELEASE',
        details: {
          reserved: sparePart.reserved,
          toRelease: quantity,
        },
      });
    }

    sparePart.reserved -= quantity;
    sparePart.status = this.determineStatus(
      sparePart.stock,
      sparePart.reserved,
      sparePart.minimumStockLevel
    );
    await sparePart.save({ session });

    return sparePart;
  }

  /**
   * Mark spare parts as sold
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If insufficient stock
   */
  async markAsSold(
    id: string | Types.ObjectId,
    quantity: number,
    session?: ClientSession
  ): Promise<ISparePart> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const sparePart = await SparePart.findById(id).session(session || null);
    if (!sparePart) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    if (sparePart.availableQuantity < quantity) {
      throw new BadRequestError('Insufficient stock', {
        code: 'INSUFFICIENT_STOCK',
        details: {
          available: sparePart.availableQuantity,
          requested: quantity,
        },
      });
    }

    if (sparePart.reserved < quantity) {
      throw new BadRequestError('Cannot sell more than reserved quantity', {
        code: 'INVALID_SALE_QUANTITY',
      });
    }

    sparePart.stock -= quantity;
    sparePart.reserved -= quantity;
    sparePart.sold += quantity;
    sparePart.status = this.determineStatus(
      sparePart.stock,
      sparePart.reserved,
      sparePart.minimumStockLevel
    );
    await sparePart.save({ session });

    return sparePart;
  }

  /**
   * Get low stock alerts
   */
  async getLowStockAlerts(): Promise<ISparePart[]> {
    return SparePart.find({
      $expr: { $lte: ['$stock', '$minimumStockLevel'] },
      status: { $nin: [SparePartStatus.DISCONTINUED, SparePartStatus.OUT_OF_STOCK] },
    }).sort({ stock: 1 });
  }

  /**
   * Soft delete a spare part
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If spare part has active reservations
   */
  async deleteSparePart(
    id: string | Types.ObjectId,
    deletedBy?: string | Types.ObjectId,
    session?: ClientSession
  ): Promise<ISparePart> {
    // Check if spare part exists and has no reservations
    const existing = await SparePart.findOne({
      _id: id,
      isDeleted: false,
    }).session(session || null);

    if (!existing) {
      throw new NotFoundError('Spare part not found', {
        code: 'SPARE_PART_NOT_FOUND',
      });
    }

    if (existing.reserved > 0) {
      throw new BadRequestError('Cannot delete spare part with active reservations', {
        code: 'ACTIVE_RESERVATIONS',
        details: {
          reserved: existing.reserved,
        },
      });
    }

    // Perform soft delete
    const sparePart = await SparePart.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        deletedAt: new Date(),
        deletedBy: deletedBy,
        status: SparePartStatus.DISCONTINUED, // Mark as discontinued when deleted
      },
      { new: true, session }
    );

    return sparePart!;
  }

  /**
   * Restore a soft-deleted spare part
   * @throws {NotFoundError} If spare part not found or not deleted
   */
  async restoreSparePart(
    id: string | Types.ObjectId,
    session?: ClientSession
  ): Promise<ISparePart> {
    const sparePart = await SparePart.findOneAndUpdate(
      {
        _id: id,
        isDeleted: true,
      },
      {
        isDeleted: false,
        deletedAt: undefined,
        deletedBy: undefined,
        status: SparePartStatus.AVAILABLE, // Restore to available status
      },
      { new: true, session }
    );

    if (!sparePart) {
      throw new NotFoundError('Deleted spare part not found', {
        code: 'DELETED_SPARE_PART_NOT_FOUND',
      });
    }

    return sparePart;
  }

  /**
   * Permanently delete a spare part (hard delete)
   * Also deletes associated image file from uploads directory
   * @throws {NotFoundError} If spare part not found
   */
  async permanentlyDeleteSparePart(
    id: string | Types.ObjectId,
    session?: ClientSession
  ): Promise<ISparePart> {
    const sparePart = await SparePart.findOneAndDelete({
      _id: id,
      isDeleted: true, // Only allow permanent deletion of soft-deleted items
    }).session(session || null);

    if (!sparePart) {
      throw new NotFoundError('Deleted spare part not found', {
        code: 'DELETED_SPARE_PART_NOT_FOUND',
      });
    }

    // After successful database deletion, clean up the image file
    if (sparePart.imagePath) {
      logger.info('Cleaning up spare part image file', {
        sparePartId: sparePart._id,
        imagePath: sparePart.imagePath,
      });

      const deleted = await FileCleanupService.deleteImageFile(sparePart.imagePath);
      if (deleted) {
        // Optionally clean up empty directories
        await FileCleanupService.cleanupEmptyDirectories(sparePart.imagePath);
      }
    }

    return sparePart;
  }

  /**
   * Discontinue a spare part (legacy method - now calls soft delete)
   * @throws {NotFoundError} If spare part not found
   * @throws {BadRequestError} If spare part has active reservations
   */
  async discontinue(id: string | Types.ObjectId, session?: ClientSession): Promise<ISparePart> {
    return this.deleteSparePart(id, undefined, session);
  }

  /**
   * List soft-deleted spare parts with pagination
   */
  async listDeletedSpareParts(
    pagination: { page: number; limit: number } = { page: 1, limit: 10 }
  ): Promise<{ data: ISparePart[]; total: number }> {
    const query = { isDeleted: true };

    const [data, total] = await Promise.all([
      SparePart.find(query)
        .sort({ deletedAt: -1 }) // Most recently deleted first
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit),
      SparePart.countDocuments(query),
    ]);

    return { data, total };
  }

  /**
   * Get sales statistics
   */
  async getSalesStatistics(): Promise<{
    totalSold: number;
    byCategory: Record<SparePartCategory, number>;
    byStatus: Record<SparePartStatus, number>;
  }> {
    const result = {
      totalSold: 0,
      byCategory: {} as Record<SparePartCategory, number>,
      byStatus: {} as Record<SparePartStatus, number>,
    };

    // Initialize all possible values with 0
    Object.values(SparePartCategory).forEach(cat => {
      result.byCategory[cat] = 0;
    });
    Object.values(SparePartStatus).forEach(status => {
      result.byStatus[status] = 0;
    });

    const spareParts = await SparePart.find();

    spareParts.forEach(sp => {
      result.totalSold += sp.sold;
      result.byCategory[sp.category] += sp.sold;
      result.byStatus[sp.status]++;
    });

    return result;
  }

  async searchSpareParts(query: string, limit = 10) {
    try {
      return SparePart.find({ $text: { $search: query } }, { score: { $meta: 'textScore' } })
        .sort({ score: { $meta: 'textScore' } })
        .limit(limit);
    } catch (error) {
      logger.error('Failed to search spare parts', {
        query,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  private determineStatus(
    stock: number,
    reserved: number,
    minimumStockLevel: number
  ): SparePartStatus {
    const available = Math.max(0, stock - reserved);

    if (available <= 0) return SparePartStatus.OUT_OF_STOCK;
    if (available <= minimumStockLevel) return SparePartStatus.LOW_STOCK;
    return SparePartStatus.AVAILABLE;
  }
}

export const sparePartService = new SparePartService();
