import { Router } from 'express';
import { paymentController } from '../controllers/payment.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const paymentRouter = Router();

// ==================== PAYMENT OPERATIONS ====================

/**
 * @route   POST /api/v1/payments/:id/preauthorize
 * @desc    Initiate payment preauthorization with gateway
 * @access  Private (Customer, Admin)
 * @body    { mobile, amount, deliveryDetails? }
 */
paymentRouter.post(
  '/:id/preauthorize',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN]),
  paymentController.initiatePreauthorization
);

/**
 * @route   POST /api/v1/payments/:id/cancel
 * @desc    Cancel payment preauthorization
 * @access  Private (Customer, Admin, Agent)
 */
paymentRouter.post(
  '/:id/cancel',
  authenticate,
  validateRole([UserRole.CUSTOMER, UserRole.ADMIN, UserRole.AGENT]),
  paymentController.cancelPreauthorization
);

/**
 * @route   POST /api/v1/payments/:id/capture
 * @desc    Capture preauthorized payment
 * @access  Private (Admin, Agent)
 */
paymentRouter.post(
  '/:id/capture',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  paymentController.capturePreauthorizedPayment
);

// ==================== GATEWAY RESPONSE ANALYTICS ====================

/**
 * @route   GET /api/v1/payments/:id/gateway-responses
 * @desc    Get raw gateway responses for debugging and audit
 * @access  Private (Admin only)
 * @query   { operation?, provider? }
 */
paymentRouter.get(
  '/:id/gateway-responses',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getGatewayResponses
);

/**
 * @route   GET /api/v1/payments/:id/analytics
 * @desc    Get payment analytics including gateway statistics
 * @access  Private (Admin only)
 */
paymentRouter.get(
  '/:id/analytics',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getPaymentAnalytics
);

// ==================== WEBHOOK HANDLING ====================

/**
 * @route   POST /api/v1/payments/:id/webhook
 * @desc    Handle webhook/callback from payment gateway
 * @access  Public (webhook endpoint - no authentication required)
 * @query   { provider? }
 * @body    Gateway-specific webhook payload
 */
paymentRouter.post(
  '/:id/webhook',
  paymentController.handleWebhook
);

// ==================== SYSTEM ANALYTICS ====================

/**
 * @route   GET /api/v1/payments/system/analytics
 * @desc    Get system-wide payment analytics
 * @access  Private (Admin only)
 * @query   { startDate?, endDate?, provider?, operation? }
 */
paymentRouter.get(
  '/system/analytics',
  authenticate,
  validateRole([UserRole.ADMIN]),
  paymentController.getSystemAnalytics
);

export default paymentRouter;
