import { Router } from 'express';
import { sparePartController } from '../controllers/spare_part.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';
import { uploadSingleImage } from '../services/image-upload.service';

const sparePartRouter = Router();

// Apply authentication middleware to all spare part routes
sparePartRouter.use(authenticate);

// ==================== CRUD OPERATIONS ====================

/**
 * @route   POST /api/v1/spare-parts
 * @desc    Create a new spare part
 * @access  Private (Admin only)
 * @body    { name, description?, price, cost, category, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
sparePartRouter.post(
  '/',
  validateRole([UserRole.ADMIN]),
  uploadSingleImage,
  sparePartController.createSparePart
);

/**
 * @route   GET /api/v1/spare-parts
 * @desc    Get all spare parts with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { search?, category?, status?, compatibleWith?, lowStock?, page?, limit? }
 */
sparePartRouter.get('/', sparePartController.getSpareParts);

/**
 * @route   GET /api/v1/spare-parts/search
 * @desc    Search spare parts
 * @access  Private (All authenticated users)
 * @query   { q, limit? }
 */
sparePartRouter.get('/search', sparePartController.searchSpareParts);

/**
 * @route   GET /api/v1/spare-parts/low-stock
 * @desc    Get spare parts with low stock
 * @access  Private (Admin, Agent)
 */
sparePartRouter.get(
  '/low-stock',
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  sparePartController.getLowStockSpareParts
);

/**
 * @route   GET /api/v1/spare-parts/statistics
 * @desc    Get spare parts sales statistics
 * @access  Private (Admin only)
 */
sparePartRouter.get(
  '/statistics',
  validateRole([UserRole.ADMIN]),
  sparePartController.getSparePartStatistics
);

/**
 * @route   GET /api/v1/spare-parts/deleted
 * @desc    Get soft-deleted spare parts
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
sparePartRouter.get(
  '/deleted',
  validateRole([UserRole.ADMIN]),
  sparePartController.getDeletedSpareParts
);

/**
 * @route   GET /api/v1/spare-parts/:id
 * @desc    Get spare part by ID
 * @access  Private (All authenticated users)
 */
sparePartRouter.get('/:id', sparePartController.getSparePartById);

/**
 * @route   DELETE /api/v1/spare-parts/:id
 * @desc    Soft delete a spare part
 * @access  Private (Admin only)
 */
sparePartRouter.delete('/:id', validateRole([UserRole.ADMIN]), sparePartController.deleteSparePart);

/**
 * @route   POST /api/v1/spare-parts/:id/restore
 * @desc    Restore a soft-deleted spare part
 * @access  Private (Admin only)
 */
sparePartRouter.post(
  '/:id/restore',
  validateRole([UserRole.ADMIN]),
  sparePartController.restoreSparePart
);

/**
 * @route   DELETE /api/v1/spare-parts/:id/permanent
 * @desc    Permanently delete a spare part (hard delete)
 * @access  Private (Admin only)
 */
sparePartRouter.delete(
  '/:id/permanent',
  validateRole([UserRole.ADMIN]),
  sparePartController.permanentlyDeleteSparePart
);

/**
 * @route   PUT /api/v1/spare-parts/:id
 * @desc    Update spare part details
 * @access  Private (Admin only)
 * @body    { name?, description?, price?, cost?, category?, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
sparePartRouter.put(
  '/:id',
  validateRole([UserRole.ADMIN]),
  uploadSingleImage,
  sparePartController.updateSparePart
);

// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.

/**
 * @route   POST /api/v1/spare-parts/:id/restock
 * @desc    Restock spare part
 * @access  Private (Admin only)
 * @body    { quantity }
 */
sparePartRouter.post(
  '/:id/restock',
  validateRole([UserRole.ADMIN]),
  sparePartController.restockSparePart
);

/**
 * @route   PUT /api/v1/spare-parts/:id/discontinue
 * @desc    Discontinue a spare part
 * @access  Private (Admin only)
 */
sparePartRouter.put(
  '/:id/discontinue',
  validateRole([UserRole.ADMIN]),
  sparePartController.discontinueSparePart
);

export default sparePartRouter;
