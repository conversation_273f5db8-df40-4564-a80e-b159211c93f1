import express, { Router } from 'express';
import userRouter from './user.routes';
import smsRouter from './sms.routes';
import notificationRouter from './notification.routes';
import orderRouter from './order.routes';
import sparePartRouter from './spare_part.routes';
import packageRouter from './package.routes';
import cylinderRouter from './cylinder.routes';
import paymentRouter from './payment.routes';
import dashboardRouter from './dashboard.routes';
import imageRouter from './image.routes';
import testRouter from './test.routes';
import path from 'path';

const appRouter = Router();

const baseRoute = '/api/v1';

// Existing routes
appRouter.use(`${baseRoute}/users`, userRouter);
appRouter.use(`${baseRoute}/sms`, smsRouter);
appRouter.use(`${baseRoute}/notifications`, notificationRouter);
appRouter.use(`${baseRoute}/orders`, orderRouter);

// Payment management routes
appRouter.use(`${baseRoute}/payments`, paymentRouter);

// Inventory management routes
appRouter.use(`${baseRoute}/spare-parts`, sparePartRouter);
appRouter.use(`${baseRoute}/packages`, packageRouter);
appRouter.use(`${baseRoute}/cylinders`, cylinderRouter);

// Dashboard routes
appRouter.use(`${baseRoute}/dashboard`, dashboardRouter);

// Image management routes
appRouter.use(`${baseRoute}/images`, imageRouter);

// Test Routes
appRouter.use(testRouter);

// Health Check Route
appRouter.get('/health', (req, res) => {
  res.send('Service is up and running');
});

// Static routes for legacy images (existing public folder)
const cylinderPath = path.join(__dirname, '..', '..', 'public', 'images', 'cylinders');
const sparePartPath = path.join(__dirname, '..', '..', 'public', 'images', 'spare_parts');
const packagePath = path.join(__dirname, '..', '..', 'public', 'images', 'packages');

// Static routes for uploaded images (new uploads folder)
const uploadsPath = path.join(__dirname, '..', '..', 'uploads', 'images');

console.log(`cylinder path: ${cylinderPath}`);
console.log(`uploads path: ${uploadsPath}`);

appRouter.use(
  // '/images/cylinders',
  '/api/v1/images/cylinders',
  express.static(
    cylinderPath
    // {
    // fallthrough: false,
    // setHeaders: (res, path) => {
    //   if (!fs.existsSync(path)) {
    //     res.redirect('/images/cylinders/default.png');
    //   }
    // },
    // }
  )
);
appRouter.use(
  // '/images/spare-parts',
  '/api/v1/images/spare-parts',
  express.static(
    sparePartPath
    // {
    // fallthrough: false,
    // setHeaders: (res, path) => {
    //   if (!fs.existsSync(path)) {
    //     res.redirect('/images/spare-parts/default.png');
    //   }
    // },
    // }
  )
);
appRouter.use(
  // '/images/packages',
  '/api/v1/images/packages',
  express.static(
    packagePath
    // {
    // fallthrough: false,
    // setHeaders: (res, path) => {
    //   if (!fs.existsSync(path)) {
    //     res.redirect('/images/packages/default.png');
    //   }
    // },
    // }
  )
);

// Serve uploaded images (new system)
appRouter.use('/api/v1/images/images', express.static(uploadsPath));

export default appRouter;
