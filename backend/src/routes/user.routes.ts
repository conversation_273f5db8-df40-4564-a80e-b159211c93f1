import { Router } from 'express';
import * as userController from '../controllers/user.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const userRouter = Router();

// Public routes
userRouter.post('/login', userController.login);
userRouter.post('/resend-otp', userController.resendOtp);
userRouter.post('/verify-otp', userController.verifyOtp);

// Protected routes
userRouter.get('/me', authenticate, userController.getCurrentUser);
userRouter.get('/:id', authenticate, userController.getSingleUser);
userRouter.put('/:id', authenticate, userController.updateUser);
userRouter.delete('/:id', authenticate, userController.deleteUser);
userRouter.patch(
  '/:id/active',
  authenticate,
  validateRole([UserRole.ADMIN]),
  userController.toggleUserActiveStatus
);
userRouter.patch(
  '/:id/on-duty',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  userController.toggleAgentOnDutyStatus
);
userRouter.patch(
  '/:id/role',
  authenticate,
  validateRole([UserRole.ADMIN]),
  userController.changeUserRole
);

// Admin only routes
userRouter.post(
  '/admin',
  authenticate,
  validateRole([UserRole.ADMIN]),
  userController.registerAdminOrAgent
);
userRouter.get(
  '/',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.SUPERVISOR]),
  userController.getAllUsers
);

// Notification routes
userRouter.post('/notifications/subscribe', authenticate, userController.subscribeToNotifications);

export default userRouter;
