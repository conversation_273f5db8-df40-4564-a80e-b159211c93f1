"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardService = exports.paymentService = exports.cylinderService = exports.packageService = exports.sparePartService = exports.orderService = exports.notificationService = exports.smsService = exports.userService = void 0;
var user_service_1 = require("./user.service");
Object.defineProperty(exports, "userService", { enumerable: true, get: function () { return user_service_1.userService; } });
var sms_services_1 = require("./sms.services");
Object.defineProperty(exports, "smsService", { enumerable: true, get: function () { return sms_services_1.smsService; } });
var notification_services_1 = require("./notification.services");
Object.defineProperty(exports, "notificationService", { enumerable: true, get: function () { return notification_services_1.notificationService; } });
var order_services_1 = require("./order.services");
Object.defineProperty(exports, "orderService", { enumerable: true, get: function () { return order_services_1.orderService; } });
var spare_part_services_1 = require("./spare_part.services");
Object.defineProperty(exports, "sparePartService", { enumerable: true, get: function () { return spare_part_services_1.sparePartService; } });
var package_services_1 = require("./package.services");
Object.defineProperty(exports, "packageService", { enumerable: true, get: function () { return package_services_1.packageService; } });
var cylinder_services_1 = require("./cylinder.services");
Object.defineProperty(exports, "cylinderService", { enumerable: true, get: function () { return cylinder_services_1.cylinderService; } });
var payment_services_1 = require("./payment.services");
Object.defineProperty(exports, "paymentService", { enumerable: true, get: function () { return payment_services_1.paymentService; } });
var dashboard_services_1 = require("./dashboard.services");
Object.defineProperty(exports, "dashboardService", { enumerable: true, get: function () { return dashboard_services_1.dashboardService; } });
//# sourceMappingURL=index.js.map