"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sparePartService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const spareParts_model_1 = require("../models/spareParts.model");
const enums_1 = require("../enums/enums");
const app_errors_1 = require("../errors/app_errors");
const image_url_generator_1 = require("../utils/image-url-generator");
const logger_1 = __importDefault(require("../config/logger"));
const file_cleanup_1 = require("../utils/file_cleanup");
class SparePartService {
    /**
     * Create a new spare part
     * @throws {DuplicateResourceError} If spare part with same category/barcode exists
     */
    async createSparePart(data, session) {
        const sessionToUse = session || (await mongoose_1.default.startSession());
        const shouldCommit = !session; // Only commit if we started the session
        try {
            if (!session)
                sessionToUse.startTransaction();
            // Check for duplicates - category must be unique
            const existing = await spareParts_model_1.SparePart.findOne({
                category: data.category,
            }).session(sessionToUse);
            if (existing) {
                throw new app_errors_1.DuplicateResourceError('Spare part with this category already exists', {
                    code: 'DUPLICATE_SPARE_PART',
                });
            }
            const initialStock = data.initialStock ?? 0;
            const initialReserved = data.initialReserved ?? 0;
            const initialSold = data.initialSold ?? 0;
            const minimumStockLevel = data.minimumStockLevel ?? 5;
            const dynamicImageUrl = (0, image_url_generator_1.getSparePartImageUrl)(data.category);
            const sparePart = new spareParts_model_1.SparePart({
                ...data,
                stock: initialStock,
                reserved: initialReserved,
                sold: initialSold,
                minimumStockLevel,
                status: this.determineStatus(initialStock, initialReserved, minimumStockLevel),
                imageUrl: dynamicImageUrl,
            });
            await sparePart.save({ session: sessionToUse });
            if (shouldCommit) {
                await sessionToUse.commitTransaction();
            }
            return sparePart;
        }
        catch (error) {
            if (shouldCommit) {
                await sessionToUse.abortTransaction();
            }
            throw error;
        }
        finally {
            if (shouldCommit) {
                sessionToUse.endSession();
            }
        }
    }
    /**
     * Update spare part details
     * @throws {NotFoundError} If spare part not found
     */
    async updateSparePart(id, updateData, session) {
        // Prevent updating inventory fields through this method
        const { stock, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
        const sparePart = await spareParts_model_1.SparePart.findByIdAndUpdate(id, safeUpdateData, {
            new: true,
            runValidators: true,
            session,
        });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        const restrictedFields = ['stock', 'reserved', 'sold', 'availableQuantity'];
        const attemptedRestrictedUpdates = Object.keys(updateData).filter(key => restrictedFields.includes(key));
        if (attemptedRestrictedUpdates.length > 0) {
            logger_1.default.warn('Restricted fields ignored in updateSparePart', {
                id,
                fields: attemptedRestrictedUpdates,
                userAttempt: updateData,
            });
        }
        // Recalculate status if minimumStockLevel changed
        if ('minimumStockLevel' in safeUpdateData) {
            sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        }
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Get spare part by ID (excludes soft-deleted items)
     * @throws {NotFoundError} If spare part not found
     */
    async getSparePartById(id, includeDeleted = false) {
        const query = { _id: id };
        if (!includeDeleted) {
            query.isDeleted = false;
        }
        const sparePart = await spareParts_model_1.SparePart.findOne(query);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * List spare parts with filtering and pagination
     */
    async listSpareParts(filters = {}, pagination = { page: 1, limit: 50 }, requestedUser) {
        const query = {};
        // Exclude soft-deleted items by default
        if (!filters.includeDeleted) {
            query.isDeleted = false;
        }
        // Text search
        if (filters.search) {
            query.$text = { $search: filters.search };
        }
        // Category filter
        if (filters.category) {
            query.category = filters.category;
        }
        // Status filter
        if (filters.status) {
            console.log('Status filter: ', {
                requestedUser,
                filters,
            });
            // query.status = filters.status;
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
            else {
                query.status = filters.status;
            }
        }
        else {
            if (requestedUser.role === enums_1.UserRole.CUSTOMER) {
                query.status = enums_1.SparePartStatus.AVAILABLE;
            }
        }
        // Compatibility filter
        if (filters.compatibleWith) {
            query.compatibleCylinderTypes = filters.compatibleWith;
        }
        // Low stock filter
        if (filters.lowStock) {
            query.$expr = { $lte: ['$stock', '$minimumStockLevel'] };
        }
        const [data, total] = await Promise.all([
            spareParts_model_1.SparePart.find(query)
                .sort({ name: 1 })
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            spareParts_model_1.SparePart.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Restock spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If quantity is invalid
     */
    async restock(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        sparePart.stock += quantity;
        sparePart.lastRestockedAt = new Date();
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Reserve spare parts for an order
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async reserve(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        sparePart.reserved += quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Release reserved spare parts
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If trying to release more than reserved
     */
    async release(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RELEASE',
                details: {
                    reserved: sparePart.reserved,
                    toRelease: quantity,
                },
            });
        }
        sparePart.reserved -= quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Mark spare parts as sold
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If insufficient stock
     */
    async markAsSold(id, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const sparePart = await spareParts_model_1.SparePart.findById(id).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (sparePart.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient stock', {
                code: 'INSUFFICIENT_STOCK',
                details: {
                    available: sparePart.availableQuantity,
                    requested: quantity,
                },
            });
        }
        if (sparePart.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot sell more than reserved quantity', {
                code: 'INVALID_SALE_QUANTITY',
            });
        }
        sparePart.stock -= quantity;
        sparePart.reserved -= quantity;
        sparePart.sold += quantity;
        sparePart.status = this.determineStatus(sparePart.stock, sparePart.reserved, sparePart.minimumStockLevel);
        await sparePart.save({ session });
        return sparePart;
    }
    /**
     * Get low stock alerts
     */
    async getLowStockAlerts() {
        return spareParts_model_1.SparePart.find({
            $expr: { $lte: ['$stock', '$minimumStockLevel'] },
            status: { $nin: [enums_1.SparePartStatus.DISCONTINUED, enums_1.SparePartStatus.OUT_OF_STOCK] },
        }).sort({ stock: 1 });
    }
    /**
     * Soft delete a spare part
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part has active reservations
     */
    async deleteSparePart(id, deletedBy, session) {
        // Check if spare part exists and has no reservations
        const existing = await spareParts_model_1.SparePart.findOne({
            _id: id,
            isDeleted: false,
        }).session(session || null);
        if (!existing) {
            throw new app_errors_1.NotFoundError('Spare part not found', {
                code: 'SPARE_PART_NOT_FOUND',
            });
        }
        if (existing.reserved > 0) {
            throw new app_errors_1.BadRequestError('Cannot delete spare part with active reservations', {
                code: 'ACTIVE_RESERVATIONS',
                details: {
                    reserved: existing.reserved,
                },
            });
        }
        // Perform soft delete
        const sparePart = await spareParts_model_1.SparePart.findByIdAndUpdate(id, {
            isDeleted: true,
            deletedAt: new Date(),
            deletedBy: deletedBy,
            status: enums_1.SparePartStatus.DISCONTINUED, // Mark as discontinued when deleted
        }, { new: true, session });
        return sparePart;
    }
    /**
     * Restore a soft-deleted spare part
     * @throws {NotFoundError} If spare part not found or not deleted
     */
    async restoreSparePart(id, session) {
        const sparePart = await spareParts_model_1.SparePart.findOneAndUpdate({
            _id: id,
            isDeleted: true,
        }, {
            isDeleted: false,
            deletedAt: undefined,
            deletedBy: undefined,
            status: enums_1.SparePartStatus.AVAILABLE, // Restore to available status
        }, { new: true, session });
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Deleted spare part not found', {
                code: 'DELETED_SPARE_PART_NOT_FOUND',
            });
        }
        return sparePart;
    }
    /**
     * Permanently delete a spare part (hard delete)
     * Also deletes associated image file from uploads directory
     * @throws {NotFoundError} If spare part not found
     */
    async permanentlyDeleteSparePart(id, session) {
        const sparePart = await spareParts_model_1.SparePart.findOneAndDelete({
            _id: id,
            isDeleted: true, // Only allow permanent deletion of soft-deleted items
        }).session(session || null);
        if (!sparePart) {
            throw new app_errors_1.NotFoundError('Deleted spare part not found', {
                code: 'DELETED_SPARE_PART_NOT_FOUND',
            });
        }
        // After successful database deletion, clean up the image file
        if (sparePart.imagePath) {
            logger_1.default.info('Cleaning up spare part image file', {
                sparePartId: sparePart._id,
                imagePath: sparePart.imagePath,
            });
            const deleted = await file_cleanup_1.FileCleanupService.deleteImageFile(sparePart.imagePath);
            if (deleted) {
                // Optionally clean up empty directories
                await file_cleanup_1.FileCleanupService.cleanupEmptyDirectories(sparePart.imagePath);
            }
        }
        return sparePart;
    }
    /**
     * Discontinue a spare part (legacy method - now calls soft delete)
     * @throws {NotFoundError} If spare part not found
     * @throws {BadRequestError} If spare part has active reservations
     */
    async discontinue(id, session) {
        return this.deleteSparePart(id, undefined, session);
    }
    /**
     * List soft-deleted spare parts with pagination
     */
    async listDeletedSpareParts(pagination = { page: 1, limit: 10 }) {
        const query = { isDeleted: true };
        const [data, total] = await Promise.all([
            spareParts_model_1.SparePart.find(query)
                .sort({ deletedAt: -1 }) // Most recently deleted first
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            spareParts_model_1.SparePart.countDocuments(query),
        ]);
        return { data, total };
    }
    /**
     * Get sales statistics
     */
    async getSalesStatistics() {
        const result = {
            totalSold: 0,
            byCategory: {},
            byStatus: {},
        };
        // Initialize all possible values with 0
        Object.values(enums_1.SparePartCategory).forEach(cat => {
            result.byCategory[cat] = 0;
        });
        Object.values(enums_1.SparePartStatus).forEach(status => {
            result.byStatus[status] = 0;
        });
        const spareParts = await spareParts_model_1.SparePart.find();
        spareParts.forEach(sp => {
            result.totalSold += sp.sold;
            result.byCategory[sp.category] += sp.sold;
            result.byStatus[sp.status]++;
        });
        return result;
    }
    async searchSpareParts(query, limit = 10) {
        try {
            return spareParts_model_1.SparePart.find({ $text: { $search: query } }, { score: { $meta: 'textScore' } })
                .sort({ score: { $meta: 'textScore' } })
                .limit(limit);
        }
        catch (error) {
            logger_1.default.error('Failed to search spare parts', {
                query,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
    }
    determineStatus(stock, reserved, minimumStockLevel) {
        const available = Math.max(0, stock - reserved);
        if (available <= 0)
            return enums_1.SparePartStatus.OUT_OF_STOCK;
        if (available <= minimumStockLevel)
            return enums_1.SparePartStatus.LOW_STOCK;
        return enums_1.SparePartStatus.AVAILABLE;
    }
}
exports.sparePartService = new SparePartService();
//# sourceMappingURL=spare_part.services.js.map