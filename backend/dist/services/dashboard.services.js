"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardService = void 0;
const mongoose_1 = require("mongoose");
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const logger_1 = __importDefault(require("../config/logger"));
const app_errors_1 = require("../errors/app_errors");
/**
 * Dashboard service providing comprehensive analytics and reporting
 * for admin, agent, and user roles
 */
class DashboardService {
    constructor() { }
    /**
     * Get comprehensive admin dashboard data
     * @param dateRange - Optional date range filter
     * @returns Admin dashboard analytics
     */
    async getAdminDashboard(dateRange) {
        try {
            logger_1.default.info('Fetching admin dashboard data', { dateRange });
            // Sales overview
            const salesOverview = await this.getSalesOverview(true);
            // Inventory status
            const inventoryStatus = await this.getInventoryStatus(true);
            // Agent performance
            const agentPerformance = await this.getAdminAgentPerformance();
            // Financial overview (only for admin)
            const financialOverview = await this.getFinancialOverview();
            // System health
            const systemHealth = await this.getSystemHealth();
            return {
                salesOverview,
                inventoryStatus,
                agentPerformance,
                financialOverview,
                systemHealth,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch admin dashboard data', {
                error: error.message,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.InternalServerError('Failed to fetch admin dashboard data');
        }
    }
    /**
     * Get agent dashboard data
     * @param agentId - Agent user ID
     * @param dateRange - Optional date range filter
     * @returns Agent dashboard analytics
     */
    async getAgentDashboard(agentId, dateRange) {
        try {
            logger_1.default.info('Fetching agent dashboard data', { agentId, dateRange });
            // Validate agent exists and has correct role
            const agent = await models_1.User.findById(agentId);
            if (!agent || agent.role !== enums_1.UserRole.AGENT) {
                throw new app_errors_1.NotFoundError('Agent not found or invalid role');
            }
            // Get assigned orders
            const assignedOrders = await models_1.Order.find({
                deliveryAgent: agentId,
                status: { $in: [enums_1.OrderStatus.CONFIRMED, enums_1.OrderStatus.IN_TRANSIT] },
            })
                .sort({ createdAt: -1 })
                .populate('customer', 'phone addresses')
                .lean();
            // Get delivery stats
            const deliveryStats = await models_1.Order.aggregate([
                { $match: { deliveryAgent: new mongoose_1.Types.ObjectId(agentId) } },
                {
                    $group: {
                        _id: null,
                        total: { $sum: 1 },
                        delivered: {
                            $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.DELIVERED] }, 1, 0] },
                        },
                        pending: {
                            $sum: {
                                $cond: [
                                    {
                                        $in: ['$status', [enums_1.OrderStatus.CONFIRMED, enums_1.OrderStatus.IN_TRANSIT]],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        failed: {
                            $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.FAILED] }, 1, 0] },
                        },
                        avgDeliveryTime: {
                            $avg: {
                                $cond: [
                                    { $eq: ['$status', enums_1.OrderStatus.DELIVERED] },
                                    { $divide: [{ $subtract: ['$deliveredAt', '$createdAt'] }, 60000] }, // in minutes
                                    null,
                                ],
                            },
                        },
                    },
                },
            ]);
            const stats = deliveryStats[0] || {
                total: 0,
                delivered: 0,
                pending: 0,
                failed: 0,
                avgDeliveryTime: 0,
            };
            // Get recent deliveries
            const recentDeliveries = await models_1.Order.find({
                deliveryAgent: agentId,
                status: enums_1.OrderStatus.DELIVERED,
            })
                .sort({ deliveredAt: -1 })
                .limit(10)
                .populate('customer', 'phone')
                .select('customer deliveryAddress totalAmount deliveredAt status')
                .lean();
            // Calculate earnings (simplified - in real app you'd have commission structure)
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const earningsData = await models_1.Order.aggregate([
                {
                    $match: {
                        deliveryAgent: new mongoose_1.Types.ObjectId(agentId),
                        status: enums_1.OrderStatus.DELIVERED,
                    },
                },
                {
                    $group: {
                        _id: null,
                        totalEarnings: { $sum: '$totalAmount' },
                        todayEarnings: {
                            $sum: {
                                $cond: [{ $gte: ['$deliveredAt', startOfDay] }, '$totalAmount', 0],
                            },
                        },
                        weekEarnings: {
                            $sum: {
                                $cond: [{ $gte: ['$deliveredAt', startOfWeek] }, '$totalAmount', 0],
                            },
                        },
                        monthEarnings: {
                            $sum: {
                                $cond: [{ $gte: ['$deliveredAt', startOfMonth] }, '$totalAmount', 0],
                            },
                        },
                    },
                },
            ]);
            const earnings = earningsData[0] || {
                totalEarnings: 0,
                todayEarnings: 0,
                weekEarnings: 0,
                monthEarnings: 0,
            };
            return {
                agentInfo: {
                    id: agent._id.toString(),
                    phone: agent.phone,
                    email: agent.email,
                    rating: agent.agentMetadata?.rating || 0,
                    isOnDuty: agent.agentMetadata?.isOnDuty || false,
                },
                orderStats: {
                    total: stats.total,
                    pending: stats.pending,
                    outForDelivery: assignedOrders.filter(o => o.status === enums_1.OrderStatus.IN_TRANSIT).length,
                    delivered: stats.delivered,
                    failed: stats.failed,
                    cancelled: 0, // Add cancelled count if needed
                },
                earnings: {
                    today: earnings.todayEarnings,
                    thisWeek: earnings.weekEarnings,
                    thisMonth: earnings.monthEarnings,
                    totalEarnings: earnings.totalEarnings,
                },
                recentDeliveries: recentDeliveries.map(delivery => ({
                    id: delivery._id.toString(),
                    customerPhone: delivery.customer?.phone || '',
                    deliveryAddress: delivery.deliveryAddress || '',
                    totalAmount: delivery.totalAmount,
                    deliveredAt: delivery.deliveredAt,
                    status: delivery.status,
                })),
                performanceMetrics: {
                    avgDeliveryTime: stats.avgDeliveryTime || 0,
                    successRate: stats.total > 0 ? (stats.delivered / stats.total) * 100 : 0,
                    customerRating: agent.agentMetadata?.rating || 0,
                    totalDeliveries: stats.delivered,
                },
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch agent dashboard data', {
                error: error.message,
                agentId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            if (error instanceof app_errors_1.NotFoundError)
                throw error;
            throw new app_errors_1.InternalServerError('Failed to fetch agent dashboard data');
        }
    }
    /**
     * Get supervisor dashboard data
     * @returns Supervisor dashboard analytics
     */
    async getSupervisorDashboard() {
        try {
            logger_1.default.info('Fetching supervisor dashboard data');
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
            const todayFilter = {
                createdAt: {
                    $gte: startOfDay,
                    $lte: endOfDay,
                },
            };
            // Today's order statistics
            const todayOrderStats = await models_1.Order.aggregate([
                { $match: todayFilter },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 },
                        totalAmount: { $sum: '$totalAmount' },
                    },
                },
            ]);
            const orderStats = {
                total: 0,
                pending: 0,
                confirmed: 0,
                outForDelivery: 0,
                delivered: 0,
                cancelled: 0,
                failed: 0,
                totalRevenue: 0,
            };
            todayOrderStats.forEach(result => {
                orderStats.total += result.count;
                orderStats.totalRevenue += result.totalAmount;
                switch (result._id) {
                    case enums_1.OrderStatus.PENDING:
                        orderStats.pending = result.count;
                        break;
                    case enums_1.OrderStatus.CONFIRMED:
                        orderStats.confirmed = result.count;
                        break;
                    case enums_1.OrderStatus.IN_TRANSIT:
                        orderStats.outForDelivery = result.count;
                        break;
                    case enums_1.OrderStatus.DELIVERED:
                        orderStats.delivered = result.count;
                        break;
                    case enums_1.OrderStatus.CANCELLED:
                        orderStats.cancelled = result.count;
                        break;
                    case enums_1.OrderStatus.FAILED:
                        orderStats.failed = result.count;
                        break;
                }
            });
            // Today's sales statistics
            const todaySalesStats = await models_1.Order.aggregate([
                {
                    $match: {
                        ...todayFilter,
                        status: { $in: [enums_1.OrderStatus.DELIVERED, enums_1.OrderStatus.CONFIRMED] },
                    },
                },
                { $unwind: '$items' },
                {
                    $group: {
                        _id: null,
                        itemsSold: { $sum: '$items.quantity' },
                        totalValue: { $sum: '$totalAmount' },
                    },
                },
            ]);
            const salesStats = todaySalesStats[0] || { itemsSold: 0, totalValue: 0 };
            // Available agents
            const availableAgents = await models_1.User.find({
                role: enums_1.UserRole.AGENT,
                'agentMetadata.isOnDuty': true,
            })
                .select('phone email agentMetadata')
                .lean();
            // Today's orders
            const todayOrders = await models_1.Order.find(todayFilter)
                .populate('customer', 'phone')
                .sort({ createdAt: -1 })
                .limit(10)
                .select('status totalAmount customer deliveryAddress createdAt')
                .lean();
            // Today's top products
            const todayTopProducts = await models_1.Order.aggregate([
                { $match: { ...todayFilter, status: enums_1.OrderStatus.DELIVERED } },
                { $unwind: '$items' },
                {
                    $group: {
                        _id: {
                            itemId: '$items.itemId',
                            itemType: '$items.itemType',
                        },
                        totalQuantity: { $sum: '$items.quantity' },
                    },
                },
                { $sort: { totalQuantity: -1 } },
                { $limit: 5 },
            ]);
            // Get product details for top products
            const topProductsWithDetails = await Promise.all(todayTopProducts.map(async (product) => {
                let productDetails = { name: 'Unknown', type: '', description: '' };
                try {
                    switch (product._id.itemType) {
                        case 'CYLINDER':
                            const cylinder = await models_1.Cylinder.findById(product._id.itemId)
                                .select('type material description')
                                .lean();
                            if (cylinder) {
                                productDetails = {
                                    name: `${cylinder.type} Cylinder`,
                                    type: cylinder.material || '',
                                    description: cylinder.description || '',
                                };
                            }
                            break;
                        case 'SPARE_PART':
                            const sparePart = await models_1.SparePart.findById(product._id.itemId)
                                .select('category description')
                                .lean();
                            if (sparePart) {
                                productDetails = {
                                    name: sparePart.category || 'Spare Part',
                                    type: sparePart.category || '',
                                    description: sparePart.description || '',
                                };
                            }
                            break;
                        case 'PACKAGE':
                            const packageItem = await models_1.Package.findById(product._id.itemId)
                                .select('name description')
                                .lean();
                            if (packageItem) {
                                productDetails = {
                                    name: packageItem.name || 'Package',
                                    type: 'package',
                                    description: packageItem.description || '',
                                };
                            }
                            break;
                    }
                }
                catch (error) {
                    logger_1.default.warn('Failed to fetch product details', {
                        productId: product._id.itemId,
                        error: error.message,
                    });
                }
                return {
                    id: {
                        itemId: product._id.itemId.toString(),
                        itemType: product._id.itemType,
                    },
                    totalQuantity: product.totalQuantity,
                    productDetails,
                };
            }));
            return {
                supervisorInfo: {},
                todayStats: {
                    orders: orderStats,
                    sales: salesStats,
                },
                availableAgents: availableAgents.map(agent => ({
                    id: agent._id.toString(),
                    phone: agent.phone || '',
                    email: agent.email,
                    agentMetadata: {
                        rating: agent.agentMetadata?.rating || 0,
                        vehicle: agent.agentMetadata?.vehicle,
                        isOnDuty: agent.agentMetadata?.isOnDuty || false,
                    },
                })),
                todayOrders: todayOrders.map(order => {
                    const customer = order.customer;
                    return {
                        id: order._id.toString(),
                        status: order.status,
                        totalAmount: order.totalAmount,
                        customer: {
                            id: customer?._id?.toString() || customer?.toString() || '',
                            phone: customer?.phone || '',
                        },
                        deliveryAddress: order.deliveryAddress || '',
                        createdAt: order.createdAt,
                    };
                }),
                todayTopProducts: topProductsWithDetails,
                restrictions: {
                    dataScope: 'TODAY_ONLY',
                    canAssignOrders: true,
                    canEditInventory: false,
                    canSeeCostData: false,
                },
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch supervisor dashboard data', {
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.InternalServerError('Failed to fetch supervisor dashboard data');
        }
    }
    /**
     * Get user dashboard data
     * @param userId - User ID
     * @param dateRange - Optional date range filter
     * @returns User dashboard analytics
     */
    async getUserDashboard(userId, dateRange) {
        try {
            logger_1.default.info('Fetching user dashboard data', { userId, dateRange });
            // Validate user exists and has correct role
            const user = await models_1.User.findById(userId);
            if (!user || user.role !== enums_1.UserRole.CUSTOMER) {
                throw new app_errors_1.NotFoundError('User not found or invalid role');
            }
            // Get recent orders (last 5)
            const recentOrders = await models_1.Order.find({ customer: userId })
                .sort({ createdAt: -1 })
                .limit(5)
                .populate('items.itemId')
                .lean();
            // Get order stats
            const orderStats = await models_1.Order.aggregate([
                { $match: { customer: new mongoose_1.Types.ObjectId(userId) } },
                {
                    $group: {
                        _id: null,
                        total: { $sum: 1 },
                        delivered: {
                            $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.DELIVERED] }, 1, 0] },
                        },
                        pending: {
                            $sum: {
                                $cond: [
                                    {
                                        $in: [
                                            '$status',
                                            [enums_1.OrderStatus.PENDING, enums_1.OrderStatus.CONFIRMED, enums_1.OrderStatus.IN_TRANSIT],
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        cancelled: {
                            $sum: { $cond: [{ $eq: ['$status', enums_1.OrderStatus.CANCELLED] }, 1, 0] },
                        },
                    },
                },
            ]);
            // Get favorite items (top 3 most ordered)
            const favoriteItems = await models_1.Order.aggregate([
                { $match: { customer: new mongoose_1.Types.ObjectId(userId) } },
                { $unwind: '$items' },
                {
                    $group: {
                        _id: '$items.itemId',
                        type: { $first: '$items.itemType' },
                        count: { $sum: '$items.quantity' },
                    },
                },
                { $sort: { count: -1 } },
                { $limit: 3 },
            ]);
            return {
                recentOrders: recentOrders.map(order => ({
                    // id: order._id,
                    id: order._id,
                    status: order.status,
                    totalAmount: order.totalAmount,
                    createdAt: order.createdAt,
                    items: order.items.map(item => ({
                        name: this.getItemName(item.itemId, item.itemType),
                        quantity: item.quantity,
                        type: this.mapItemType(item.itemType),
                    })),
                })),
                orderStats: orderStats[0] || {
                    total: 0,
                    delivered: 0,
                    pending: 0,
                    cancelled: 0,
                },
                favoriteItems: await Promise.all(favoriteItems.map(async (item) => ({
                    id: item._id,
                    name: await this.getItemNameById(item._id, item.type),
                    type: this.mapItemType(item.type),
                    purchaseCount: item.count,
                }))),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to fetch user dashboard data', {
                error: error.message,
                userId,
                dateRange,
                timestamp: new Date().toISOString(),
            });
            if (error instanceof app_errors_1.NotFoundError)
                throw error;
            throw new app_errors_1.InternalServerError('Failed to fetch user dashboard data');
        }
    }
    /**
     * Get reports based on date range and user role
     * @param startDate - Start date for report
     * @param endDate - End date for report
     * @param userRole - User role requesting the report
     * @returns Report data
     */
    async getReports(startDate, endDate, userRole) {
        try {
            logger_1.default.info('Generating reports', { startDate, endDate, userRole });
            // Generate comprehensive sales overview data
            const salesOverview = await this.generateSalesOverview(startDate, endDate);
            // Generate financial overview data (admin only)
            const financialOverview = userRole === enums_1.UserRole.ADMIN
                ? await this.generateFinancialOverview(startDate, endDate)
                : null;
            // Generate inventory status data
            const inventoryStatus = await this.generateInventoryStatus(startDate, endDate);
            // Generate agent performance data
            const agentPerformance = await this.generateAgentPerformance(startDate, endDate);
            return {
                salesOverview,
                financialOverview,
                inventoryStatus,
                agentPerformance,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate reports', {
                error: error.message,
                startDate,
                endDate,
                userRole,
                timestamp: new Date().toISOString(),
            });
            throw new app_errors_1.InternalServerError('Failed to generate reports');
        }
    }
    /**
     * Get sales overview data
     * @param includeProfit - Whether to include profit data (admin only)
     * @returns Sales overview data
     */
    async getSalesOverview(includeProfit = false) {
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        // Current week sales
        const currentWeek = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: oneWeekAgo, $lte: now },
                },
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: 1 },
                    totalRevenue: { $sum: '$totalAmount' },
                },
            },
        ]);
        // Previous week sales for trend calculation
        const previousWeek = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: twoWeeksAgo, $lte: oneWeekAgo },
                },
            },
            {
                $group: {
                    _id: null,
                    totalSales: { $sum: 1 },
                    totalRevenue: { $sum: '$totalAmount' },
                },
            },
        ]);
        const currentWeekData = currentWeek[0] || { totalSales: 0, totalRevenue: 0 };
        const previousWeekData = previousWeek[0] || { totalSales: 0, totalRevenue: 0 };
        // Calculate weekly trend
        const weeklyTrend = previousWeekData.totalSales > 0
            ? ((currentWeekData.totalSales - previousWeekData.totalSales) /
                previousWeekData.totalSales) *
                100
            : 100; // if no previous sales, consider 100% growth
        return {
            totalSales: currentWeekData.totalSales,
            totalRevenue: currentWeekData.totalRevenue,
            dailyAverage: currentWeekData.totalSales / 7,
            weeklyTrend: Math.round(weeklyTrend),
        };
    }
    /**
     * Get inventory status data
     * @param includeCost - Whether to include cost data (admin only)
     * @returns Inventory status data
     */
    async getInventoryStatus(includeCost = false) {
        const [cylinders, spareParts] = await Promise.all([
            models_1.Cylinder.aggregate([
                {
                    $group: {
                        _id: null,
                        total: { $sum: '$quantity' },
                        available: { $sum: { $subtract: ['$quantity', '$reserved'] } },
                    },
                },
            ]),
            models_1.SparePart.countDocuments({
                $expr: { $lte: ['$currentStock', '$minimumStockLevel'] },
            }),
        ]);
        const totalSpareParts = await models_1.SparePart.countDocuments();
        return {
            totalCylinders: cylinders[0]?.total || 0,
            availableCylinders: cylinders[0]?.available || 0,
            totalSpareParts,
            lowStockSpareParts: spareParts || 0,
        };
    }
    /**
     * Get financial overview data (admin only)
     * @returns Financial overview data
     */
    async getFinancialOverview() {
        const now = new Date();
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        // Get revenue and profit by calculating cost vs selling price
        const result = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: oneMonthAgo, $lte: now },
                },
            },
            { $unwind: '$items' },
            {
                $lookup: {
                    from: 'cylinders',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'CYLINDER'] }],
                                },
                            },
                        },
                    ],
                    as: 'cylinderData',
                },
            },
            {
                $lookup: {
                    from: 'spareparts',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'SPARE_PART'] }],
                                },
                            },
                        },
                    ],
                    as: 'sparePartData',
                },
            },
            {
                $lookup: {
                    from: 'packages',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'PACKAGE'] }],
                                },
                            },
                        },
                    ],
                    as: 'packageData',
                },
            },
            {
                $addFields: {
                    itemPrice: {
                        $cond: [
                            { $eq: ['$items.itemType', 'CYLINDER'] },
                            { $arrayElemAt: ['$cylinderData.price', 0] },
                            {
                                $cond: [
                                    { $eq: ['$items.itemType', 'SPARE_PART'] },
                                    { $arrayElemAt: ['$sparePartData.price', 0] },
                                    { $arrayElemAt: ['$packageData.price', 0] },
                                ],
                            },
                        ],
                    },
                    itemCost: {
                        $cond: [
                            { $eq: ['$items.itemType', 'CYLINDER'] },
                            { $arrayElemAt: ['$cylinderData.cost', 0] },
                            {
                                $cond: [
                                    { $eq: ['$items.itemType', 'SPARE_PART'] },
                                    { $arrayElemAt: ['$sparePartData.cost', 0] },
                                    { $arrayElemAt: ['$packageData.cost', 0] },
                                ],
                            },
                        ],
                    },
                },
            },
            {
                $group: {
                    _id: null,
                    totalRevenue: { $sum: { $multiply: ['$itemPrice', '$items.quantity'] } },
                    totalCost: { $sum: { $multiply: ['$itemCost', '$items.quantity'] } },
                },
            },
            {
                $addFields: {
                    totalProfit: { $subtract: ['$totalRevenue', '$totalCost'] },
                },
            },
        ]);
        const financialData = result[0] || { totalRevenue: 0, totalProfit: 0, totalCost: 0 };
        // Get most profitable items
        const profitableItems = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: oneMonthAgo, $lte: now },
                },
            },
            { $unwind: '$items' },
            {
                $lookup: {
                    from: 'cylinders',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'CYLINDER'] }],
                                },
                            },
                        },
                    ],
                    as: 'cylinderData',
                },
            },
            {
                $lookup: {
                    from: 'spareparts',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'SPARE_PART'] }],
                                },
                            },
                        },
                    ],
                    as: 'sparePartData',
                },
            },
            {
                $lookup: {
                    from: 'packages',
                    let: { itemId: '$items.itemId', itemType: '$items.itemType' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$_id', '$$itemId'] }, { $eq: ['$$itemType', 'PACKAGE'] }],
                                },
                            },
                        },
                    ],
                    as: 'packageData',
                },
            },
            {
                $addFields: {
                    itemPrice: {
                        $cond: [
                            { $eq: ['$items.itemType', 'CYLINDER'] },
                            { $arrayElemAt: ['$cylinderData.price', 0] },
                            {
                                $cond: [
                                    { $eq: ['$items.itemType', 'SPARE_PART'] },
                                    { $arrayElemAt: ['$sparePartData.price', 0] },
                                    { $arrayElemAt: ['$packageData.price', 0] },
                                ],
                            },
                        ],
                    },
                    itemCost: {
                        $cond: [
                            { $eq: ['$items.itemType', 'CYLINDER'] },
                            { $arrayElemAt: ['$cylinderData.cost', 0] },
                            {
                                $cond: [
                                    { $eq: ['$items.itemType', 'SPARE_PART'] },
                                    { $arrayElemAt: ['$sparePartData.cost', 0] },
                                    { $arrayElemAt: ['$packageData.cost', 0] },
                                ],
                            },
                        ],
                    },
                },
            },
            {
                $group: {
                    _id: {
                        itemId: '$items.itemId',
                        itemType: '$items.itemType',
                    },
                    totalRevenue: { $sum: { $multiply: ['$itemPrice', '$items.quantity'] } },
                    totalCost: { $sum: { $multiply: ['$itemCost', '$items.quantity'] } },
                    totalQuantity: { $sum: '$items.quantity' },
                },
            },
            {
                $addFields: {
                    totalProfit: { $subtract: ['$totalRevenue', '$totalCost'] },
                },
            },
            { $sort: { totalProfit: -1 } },
            { $limit: 5 },
        ]);
        // Map to item names
        const itemsWithNames = await Promise.all(profitableItems.map(async (item) => ({
            id: item._id.itemId,
            name: await this.getItemNameById(item._id.itemId, item._id.itemType),
            type: this.mapItemType(item._id.itemType),
            profit: item.totalProfit || 0,
        })));
        return {
            totalRevenue: financialData.totalRevenue,
            totalProfit: financialData.totalProfit,
            profitMargin: financialData.totalRevenue > 0
                ? (financialData.totalProfit / financialData.totalRevenue) * 100
                : 0,
            mostProfitableItems: itemsWithNames,
        };
    }
    /**
     * Get system health data (admin only)
     * @returns System health data
     */
    async getSystemHealth() {
        const [totalUsers, activeUsers, ordersThisMonth, paymentStats] = await Promise.all([
            models_1.User.countDocuments(),
            models_1.User.countDocuments({ isActive: true }),
            models_1.Order.countDocuments({
                createdAt: { $gte: new Date(new Date().setDate(1)) },
            }),
            models_1.Payment.aggregate([
                {
                    $group: {
                        _id: null,
                        total: { $sum: 1 },
                        success: {
                            $sum: {
                                $cond: [{ $eq: ['$status', enums_1.PaymentStatus.CAPTURED] }, 1, 0],
                            },
                        },
                    },
                },
            ]),
        ]);
        const paymentData = paymentStats[0] || { total: 0, success: 0 };
        return {
            totalUsers,
            activeUsers,
            ordersThisMonth,
            paymentSuccessRate: paymentData.total > 0 ? (paymentData.success / paymentData.total) * 100 : 100,
        };
    }
    /**
     * Get total revenue for a date range
     * @param startDate - Start date
     * @param endDate - End date
     * @returns Total revenue
     */
    async getTotalRevenue(startDate, endDate) {
        const result = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $group: {
                    _id: null,
                    total: { $sum: '$totalAmount' },
                },
            },
        ]);
        return result[0]?.total || 0;
    }
    /**
     * Get profit data for a date range (admin only)
     * @param startDate - Start date
     * @param endDate - End date
     * @returns Profit data
     */
    async getProfitData(startDate, endDate) {
        // This is a simplified version - in a real app you'd need to calculate actual costs
        const result = await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $lookup: {
                    from: 'payments',
                    localField: '_id',
                    foreignField: 'orderId',
                    as: 'payment',
                },
            },
            { $unwind: '$payment' },
            {
                $group: {
                    _id: null,
                    totalProfit: { $sum: { $subtract: ['$totalAmount', '$payment.amount'] } },
                },
            },
        ]);
        return {
            totalProfit: result[0]?.totalProfit || 0,
        };
    }
    /**
     * Get item name from item object
     * @param item - Item object
     * @param itemType - Item type
     * @returns Item name
     */
    getItemName(item, itemType) {
        if (!item)
            return 'Unknown Item';
        switch (itemType) {
            case 'CYLINDER':
                return `${item.type} Cylinder`;
            case 'SPARE_PART':
                return item.category || 'Spare Part';
            case 'PACKAGE':
                return item.name || 'Package';
            default:
                return 'Unknown Item';
        }
    }
    /**
     * Get item name by ID and type
     * @param itemId - Item ID
     * @param itemType - Item type
     * @returns Item name
     */
    async getItemNameById(itemId, itemType) {
        try {
            let item;
            switch (itemType) {
                case 'CYLINDER':
                    item = await models_1.Cylinder.findById(itemId).lean();
                    return `${item?.type} Cylinder` || 'Cylinder';
                case 'SPARE_PART':
                    item = await models_1.SparePart.findById(itemId).lean();
                    return item?.category || 'Spare Part';
                case 'PACKAGE':
                    item = await models_1.Package.findById(itemId).lean();
                    return item?.name || 'Package';
                default:
                    return 'Unknown Item';
            }
        }
        catch (error) {
            return 'Unknown Item';
        }
    }
    /**
     * Map item type string to standardized type
     * @param itemType - Item type string
     * @returns Standardized item type
     */
    mapItemType(itemType) {
        switch (itemType) {
            case 'CYLINDER':
                return 'cylinder';
            case 'SPARE_PART':
                return 'spare_part';
            case 'PACKAGE':
                return 'package';
            default:
                return 'cylinder'; // default fallback
        }
    }
    /**
     * Get admin agent performance data
     */
    async getAdminAgentPerformance() {
        return await models_1.User.aggregate([
            { $match: { role: enums_1.UserRole.AGENT } },
            {
                $lookup: {
                    from: 'orders',
                    localField: '_id',
                    foreignField: 'deliveryAgent',
                    as: 'orders',
                },
            },
            {
                $project: {
                    name: { $ifNull: ['$username', '$phone'] },
                    completedDeliveries: {
                        $size: {
                            $filter: {
                                input: '$orders',
                                as: 'order',
                                cond: { $eq: ['$$order.status', enums_1.OrderStatus.DELIVERED] },
                            },
                        },
                    },
                    avgRating: { $ifNull: ['$agentMetadata.rating', 0] },
                    lastActive: { $max: '$orders.createdAt' },
                },
            },
            { $sort: { completedDeliveries: -1 } },
        ]);
    }
    /**
     * Generate comprehensive sales overview data
     */
    async generateSalesOverview(startDate, endDate) {
        try {
            // Basic order statistics
            const totalOrders = await models_1.Order.countDocuments({
                createdAt: { $gte: startDate, $lte: endDate },
            });
            const completedOrders = await models_1.Order.countDocuments({
                status: enums_1.OrderStatus.DELIVERED,
                createdAt: { $gte: startDate, $lte: endDate },
            });
            const pendingOrders = await models_1.Order.countDocuments({
                status: { $in: [enums_1.OrderStatus.CONFIRMED, enums_1.OrderStatus.IN_TRANSIT] },
                createdAt: { $gte: startDate, $lte: endDate },
            });
            const cancelledOrders = await models_1.Order.countDocuments({
                status: { $in: [enums_1.OrderStatus.CANCELLED, enums_1.OrderStatus.FAILED] },
                createdAt: { $gte: startDate, $lte: endDate },
            });
            // Revenue calculations
            const totalRevenue = await this.getTotalRevenue(startDate, endDate);
            // Calculate daily average
            const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
            const dailyAverage = totalRevenue / daysDiff;
            // Calculate weekly trend (compare with previous period)
            const previousPeriodStart = new Date(startDate.getTime() - (endDate.getTime() - startDate.getTime()));
            const previousRevenue = await this.getTotalRevenue(previousPeriodStart, startDate);
            const weeklyTrend = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
            // Order completion rate
            const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
            // Average order value
            const avgOrderValue = completedOrders > 0 ? totalRevenue / completedOrders : 0;
            // Top selling items
            const topSellingItems = await this.getTopSellingItems(startDate, endDate, 5);
            return {
                totalSales: totalOrders,
                totalRevenue,
                dailyAverage,
                weeklyTrend,
                completedOrders,
                pendingOrders,
                cancelledOrders,
                completionRate,
                avgOrderValue,
                topSellingItems,
                ordersByStatus: {
                    completed: completedOrders,
                    pending: pendingOrders,
                    cancelled: cancelledOrders,
                },
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate sales overview', { error: error.message });
            throw error;
        }
    }
    /**
     * Generate comprehensive financial overview data
     */
    async generateFinancialOverview(startDate, endDate) {
        try {
            const totalRevenue = await this.getTotalRevenue(startDate, endDate);
            const profitData = await this.getProfitData(startDate, endDate);
            // Calculate profit margin
            const profitMargin = totalRevenue > 0 ? (profitData.totalProfit / totalRevenue) * 100 : 0;
            // Get most profitable items
            const mostProfitableItems = await this.getMostProfitableItems(startDate, endDate, 5);
            // Revenue breakdown by category
            const revenueBreakdown = await this.getRevenueBreakdown(startDate, endDate);
            // Monthly revenue trend
            const monthlyTrend = await this.getMonthlyRevenueTrend(startDate, endDate);
            return {
                totalRevenue,
                totalProfit: profitData.totalProfit,
                profitMargin,
                mostProfitableItems,
                revenueBreakdown,
                monthlyTrend,
                costAnalysis: {
                    totalCost: totalRevenue - profitData.totalProfit,
                    costPercentage: totalRevenue > 0 ? ((totalRevenue - profitData.totalProfit) / totalRevenue) * 100 : 0,
                },
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate financial overview', { error: error.message });
            throw error;
        }
    }
    /**
     * Generate comprehensive inventory status data
     */
    async generateInventoryStatus(startDate, endDate) {
        try {
            // Cylinder inventory
            const totalCylinders = await models_1.Cylinder.countDocuments();
            const availableCylinders = await models_1.Cylinder.countDocuments({
                status: enums_1.CylinderStatus.Active,
            });
            const lowStockCylinders = await models_1.Cylinder.countDocuments({
                currentStock: { $lte: 5 }, // Assuming low stock threshold is 5
            });
            // Spare parts inventory
            const totalSpareParts = await models_1.SparePart.countDocuments();
            const lowStockSpareParts = await models_1.SparePart.countDocuments({
                currentStock: { $lte: '$minimumStockLevel' },
            });
            // Package inventory
            const totalPackages = await models_1.Package.countDocuments();
            const lowStockPackages = await models_1.Package.countDocuments({
                currentStock: { $lte: '$minimumStockLevel' },
            });
            // Inventory movements during the period
            const inventoryMovements = await this.getInventoryMovements(startDate, endDate);
            // Stock alerts
            const stockAlerts = await this.getStockAlerts();
            return {
                totalCylinders,
                availableCylinders,
                totalSpareParts,
                lowStockSpareParts,
                totalPackages,
                lowStockPackages,
                lowStockCylinders,
                inventoryValue: await this.calculateInventoryValue(),
                inventoryMovements,
                stockAlerts,
                inventoryHealth: {
                    cylinderUtilization: totalCylinders > 0 ? (availableCylinders / totalCylinders) * 100 : 0,
                    sparePartsHealth: totalSpareParts > 0
                        ? ((totalSpareParts - lowStockSpareParts) / totalSpareParts) * 100
                        : 0,
                    packagesHealth: totalPackages > 0 ? ((totalPackages - lowStockPackages) / totalPackages) * 100 : 0,
                },
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate inventory status', { error: error.message });
            throw error;
        }
    }
    /**
     * Generate agent performance data
     */
    async generateAgentPerformance(startDate, endDate) {
        try {
            const agentPerformance = await models_1.User.aggregate([
                {
                    $match: {
                        role: enums_1.UserRole.AGENT,
                    },
                },
                {
                    $lookup: {
                        from: 'orders',
                        localField: '_id',
                        foreignField: 'deliveryAgent',
                        as: 'orders',
                        pipeline: [
                            {
                                $match: {
                                    createdAt: { $gte: startDate, $lte: endDate },
                                },
                            },
                        ],
                    },
                },
                {
                    $project: {
                        _id: 1,
                        phone: 1,
                        email: 1,
                        name: { $ifNull: ['$name', '$phone'] },
                        completedDeliveries: {
                            $size: {
                                $filter: {
                                    input: '$orders',
                                    cond: { $eq: ['$$this.status', enums_1.OrderStatus.DELIVERED] },
                                },
                            },
                        },
                        totalOrders: { $size: '$orders' },
                        avgRating: { $ifNull: ['$agentMetadata.rating', 0] },
                        lastActive: { $max: '$orders.createdAt' },
                        totalRevenue: {
                            $sum: {
                                $map: {
                                    input: {
                                        $filter: {
                                            input: '$orders',
                                            cond: { $eq: ['$$this.status', enums_1.OrderStatus.DELIVERED] },
                                        },
                                    },
                                    as: 'order',
                                    in: '$$order.totalAmount',
                                },
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        completionRate: {
                            $cond: {
                                if: { $gt: ['$totalOrders', 0] },
                                then: { $multiply: [{ $divide: ['$completedDeliveries', '$totalOrders'] }, 100] },
                                else: 0,
                            },
                        },
                        avgOrderValue: {
                            $cond: {
                                if: { $gt: ['$completedDeliveries', 0] },
                                then: { $divide: ['$totalRevenue', '$completedDeliveries'] },
                                else: 0,
                            },
                        },
                    },
                },
                { $sort: { completedDeliveries: -1 } },
                { $limit: 10 },
            ]);
            // Calculate team statistics
            const totalAgents = await models_1.User.countDocuments({ role: enums_1.UserRole.AGENT });
            const activeAgents = await models_1.User.countDocuments({
                role: enums_1.UserRole.AGENT,
                'agentMetadata.isOnDuty': true,
            });
            const teamStats = {
                totalAgents,
                activeAgents,
                averageRating: agentPerformance.length > 0
                    ? agentPerformance.reduce((sum, agent) => sum + agent.avgRating, 0) /
                        agentPerformance.length
                    : 0,
                totalDeliveries: agentPerformance.reduce((sum, agent) => sum + agent.completedDeliveries, 0),
            };
            return {
                agentPerformance,
                teamStats,
                topPerformers: agentPerformance.slice(0, 3),
            };
        }
        catch (error) {
            logger_1.default.error('Failed to generate agent performance', { error: error.message });
            throw error;
        }
    }
    /**
     * Helper method to get top selling items
     */
    async getTopSellingItems(startDate, endDate, limit = 5) {
        return await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            { $unwind: '$items' },
            {
                $group: {
                    _id: {
                        itemId: '$items.itemId',
                        itemType: '$items.itemType',
                    },
                    totalQuantity: { $sum: '$items.quantity' },
                    totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
                },
            },
            { $sort: { totalQuantity: -1 } },
            { $limit: limit },
            {
                $lookup: {
                    from: 'cylinders',
                    localField: '_id.itemId',
                    foreignField: '_id',
                    as: 'cylinderDetails',
                },
            },
            {
                $lookup: {
                    from: 'spareparts',
                    localField: '_id.itemId',
                    foreignField: '_id',
                    as: 'sparePartDetails',
                },
            },
            {
                $lookup: {
                    from: 'packages',
                    localField: '_id.itemId',
                    foreignField: '_id',
                    as: 'packageDetails',
                },
            },
            {
                $project: {
                    _id: 1,
                    totalQuantity: 1,
                    totalRevenue: 1,
                    itemDetails: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$_id.itemType', 'cylinder'] },
                                    then: { $arrayElemAt: ['$cylinderDetails', 0] },
                                },
                                {
                                    case: { $eq: ['$_id.itemType', 'spare_part'] },
                                    then: { $arrayElemAt: ['$sparePartDetails', 0] },
                                },
                                {
                                    case: { $eq: ['$_id.itemType', 'package'] },
                                    then: { $arrayElemAt: ['$packageDetails', 0] },
                                },
                            ],
                            default: null,
                        },
                    },
                },
            },
        ]);
    }
    /**
     * Helper method to get most profitable items
     */
    async getMostProfitableItems(startDate, endDate, limit = 5) {
        return await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            { $unwind: '$items' },
            {
                $group: {
                    _id: {
                        itemId: '$items.itemId',
                        itemType: '$items.itemType',
                    },
                    totalQuantity: { $sum: '$items.quantity' },
                    totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
                    totalCost: { $sum: { $multiply: ['$items.quantity', '$items.cost'] } },
                },
            },
            {
                $addFields: {
                    profit: { $subtract: ['$totalRevenue', '$totalCost'] },
                    profitMargin: {
                        $cond: {
                            if: { $gt: ['$totalRevenue', 0] },
                            then: {
                                $multiply: [
                                    { $divide: [{ $subtract: ['$totalRevenue', '$totalCost'] }, '$totalRevenue'] },
                                    100,
                                ],
                            },
                            else: 0,
                        },
                    },
                },
            },
            { $sort: { profit: -1 } },
            { $limit: limit },
        ]);
    }
    /**
     * Helper method to get revenue breakdown by category
     */
    async getRevenueBreakdown(startDate, endDate) {
        return await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            { $unwind: '$items' },
            {
                $group: {
                    _id: '$items.itemType',
                    totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
                    totalQuantity: { $sum: '$items.quantity' },
                },
            },
            { $sort: { totalRevenue: -1 } },
        ]);
    }
    /**
     * Helper method to get monthly revenue trend
     */
    async getMonthlyRevenueTrend(startDate, endDate) {
        return await models_1.Order.aggregate([
            {
                $match: {
                    status: enums_1.OrderStatus.DELIVERED,
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    totalRevenue: { $sum: '$totalAmount' },
                    totalOrders: { $sum: 1 },
                },
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } },
        ]);
    }
    /**
     * Helper method to get inventory movements
     */
    async getInventoryMovements(startDate, endDate) {
        // This would track inventory changes - for now return basic data
        const ordersWithItems = await models_1.Order.aggregate([
            {
                $match: {
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            { $unwind: '$items' },
            {
                $group: {
                    _id: '$items.itemType',
                    totalMovement: { $sum: '$items.quantity' },
                },
            },
        ]);
        return ordersWithItems;
    }
    /**
     * Helper method to get stock alerts
     */
    async getStockAlerts() {
        const lowStockCylinders = await models_1.Cylinder.find({
            currentStock: { $lte: 5 },
        }).select('type material currentStock minimumStockLevel');
        const lowStockSpareParts = await models_1.SparePart.find({
            $expr: { $lte: ['$currentStock', '$minimumStockLevel'] },
        }).select('category description currentStock minimumStockLevel');
        const lowStockPackages = await models_1.Package.find({
            $expr: { $lte: ['$currentStock', '$minimumStockLevel'] },
        }).select('name description currentStock minimumStockLevel');
        return {
            cylinders: lowStockCylinders,
            spareParts: lowStockSpareParts,
            packages: lowStockPackages,
        };
    }
    /**
     * Helper method to calculate total inventory value
     */
    async calculateInventoryValue() {
        const cylinderValue = await models_1.Cylinder.aggregate([
            {
                $group: {
                    _id: null,
                    totalValue: { $sum: { $multiply: ['$currentStock', '$price'] } },
                },
            },
        ]);
        const sparePartValue = await models_1.SparePart.aggregate([
            {
                $group: {
                    _id: null,
                    totalValue: { $sum: { $multiply: ['$currentStock', '$price'] } },
                },
            },
        ]);
        const packageValue = await models_1.Package.aggregate([
            {
                $group: {
                    _id: null,
                    totalValue: { $sum: { $multiply: ['$currentStock', '$price'] } },
                },
            },
        ]);
        return {
            cylinders: cylinderValue[0]?.totalValue || 0,
            spareParts: sparePartValue[0]?.totalValue || 0,
            packages: packageValue[0]?.totalValue || 0,
            total: (cylinderValue[0]?.totalValue || 0) +
                (sparePartValue[0]?.totalValue || 0) +
                (packageValue[0]?.totalValue || 0),
        };
    }
}
exports.dashboardService = new DashboardService();
//# sourceMappingURL=dashboard.services.js.map