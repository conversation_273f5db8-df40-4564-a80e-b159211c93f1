"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = void 0;
const app_errors_1 = require("../errors/app_errors");
const models_1 = require("../models");
const notificationUtil = __importStar(require("../utils/notification_utils"));
const notification_utils_1 = require("../utils/notification_utils");
const logger_1 = __importDefault(require("../config/logger"));
class NotificationService {
    /**
     * Send notification to a specific user
     */
    async sendToUser(userId, payload) {
        try {
            // Validate input
            if (!userId)
                throw new app_errors_1.BadRequestError('User ID is required');
            if (!payload?.title || !payload?.body) {
                throw new app_errors_1.BadRequestError('Title and body are required');
            }
            const user = await models_1.User.findById(userId).select('notification');
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            if (!user.notification) {
                throw new app_errors_1.NotFoundError('Notification settings not configured');
            }
            if (!user.notification.isEnabled) {
                throw new app_errors_1.BadRequestError('Notifications are disabled for this user');
            }
            if (!user.notification.fcmToken) {
                throw new app_errors_1.BadRequestError('FCM token not registered for this user');
            }
            // Create notification record
            const notificationRecord = await models_1.Notification.create({
                userId,
                title: payload.title,
                body: payload.body,
                data: payload.data,
                imageUrl: payload.imageUrl,
                status: 'pending',
            });
            try {
                // Send the actual push notification
                const result = await notificationUtil.sendPushNotification(user.notification.fcmToken, payload);
                // Update notification status
                await models_1.Notification.findByIdAndUpdate(notificationRecord._id, {
                    status: 'delivered',
                    deliveredAt: new Date(),
                    result,
                });
                logger_1.default.info(`Notification sent to user ${userId}`, {
                    notificationId: notificationRecord._id,
                    title: payload.title,
                });
                return {
                    success: true,
                    //   notificationId: notificationRecord._id,
                    details: result,
                };
            }
            catch (error) {
                await models_1.Notification.findByIdAndUpdate(notificationRecord._id, {
                    status: 'failed',
                    error: error.message,
                });
                logger_1.default.error(`Failed to send notification to user ${userId}`, {
                    error: error.message,
                    notificationId: notificationRecord._id,
                });
                throw new app_errors_1.InternalServerError('Failed to send notification');
            }
        }
        catch (error) {
            logger_1.default.error('NotificationService.sendToUser error', {
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Send notification to all users subscribed to a topic
     */
    async sendToTopic(topic, payload, options) {
        try {
            // Validate input
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            if (!payload?.title || !payload?.body) {
                throw new app_errors_1.BadRequestError('Title and body are required');
            }
            // Get all users subscribed to this topic
            const query = {
                'notification.topics': topic,
                'notification.isEnabled': true,
            };
            if (options?.onlyActiveUsers) {
                query.isActive = true;
            }
            const users = await models_1.User.find(query).select('notification isActive').lean();
            if (!users.length) {
                logger_1.default.warn(`No users found subscribed to topic ${topic}`);
                return {
                    success: true,
                    message: 'No subscribed users found',
                    count: 0,
                };
            }
            // Filter users with valid FCM tokens
            const validUsers = users.filter(u => u.notification?.fcmToken);
            const tokens = validUsers.map(u => u.notification.fcmToken);
            if (!tokens.length) {
                logger_1.default.warn(`No valid FCM tokens found for topic ${topic}`);
                return {
                    success: true,
                    message: 'No valid FCM tokens found',
                    count: 0,
                };
            }
            // Create bulk notification records
            const notificationRecords = await models_1.Notification.insertMany(validUsers.map(user => ({
                userId: user._id,
                title: payload.title,
                body: payload.body,
                data: payload.data,
                imageUrl: payload.imageUrl,
                topic,
                status: 'pending',
            })));
            try {
                // Send to topic
                const result = await notificationUtil.sendToTopic(topic, payload);
                // Update all notification records
                await models_1.Notification.updateMany({ _id: { $in: notificationRecords.map(n => n._id) } }, {
                    status: 'delivered',
                    deliveredAt: new Date(),
                    result,
                });
                logger_1.default.info(`Notification sent to topic ${topic}`, {
                    count: validUsers.length,
                    title: payload.title,
                });
                return {
                    success: true,
                    count: validUsers.length,
                    details: result,
                };
            }
            catch (error) {
                await models_1.Notification.updateMany({ _id: { $in: notificationRecords.map(n => n._id) } }, {
                    status: 'failed',
                    error: error.message,
                });
                logger_1.default.error(`Failed to send notification to topic ${topic}`, {
                    error: error.message,
                    count: validUsers.length,
                });
                throw new app_errors_1.InternalServerError('Failed to send topic notification');
            }
        }
        catch (error) {
            logger_1.default.error('NotificationService.sendToTopic error', {
                topic,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Update user's FCM token
     */
    async updateFcmToken(userId, token) {
        try {
            if (!userId || !token) {
                throw new app_errors_1.BadRequestError('User ID and token are required');
            }
            const user = await models_1.User.findByIdAndUpdate(userId, {
                $set: {
                    'notification.fcmToken': token,
                    'notification.isEnabled': true,
                },
            }, { new: true });
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            logger_1.default.info(`Updated FCM token for user ${userId}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to update FCM token', {
                userId,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Subscribe user to notification topic
     */
    async subscribeToTopic(userId, topic) {
        try {
            if (!Object.values(notification_utils_1.NotificationTopic).includes(topic)) {
                throw new app_errors_1.BadRequestError('Invalid notification topic');
            }
            const user = await models_1.User.findById(userId).select('notification');
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            if (!user.notification) {
                throw new app_errors_1.BadRequestError('Notification settings not initialized');
            }
            // Check if already subscribed
            if (user.notification.topics?.includes(topic)) {
                return { success: true };
            }
            // Update in database
            await models_1.User.findByIdAndUpdate(userId, { $addToSet: { 'notification.topics': topic } });
            // Subscribe with FCM if token exists
            if (user.notification.fcmToken) {
                try {
                    await notificationUtil.subscribeToTopic([user.notification.fcmToken], topic);
                }
                catch (error) {
                    logger_1.default.error('FCM topic subscription failed', {
                        userId,
                        topic,
                        error: error.message,
                    });
                    // Continue even if FCM fails - we'll sync later
                }
            }
            logger_1.default.info(`User ${userId} subscribed to topic ${topic}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to subscribe to topic', {
                userId,
                topic,
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Toggle notification enable/disable
     */
    async toggleNotifications(userId, enabled) {
        try {
            const user = await models_1.User.findByIdAndUpdate(userId, { $set: { 'notification.isEnabled': enabled } }, { new: true });
            if (!user)
                throw new app_errors_1.NotFoundError('User not found');
            logger_1.default.info(`Notifications ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);
            return { success: true };
        }
        catch (error) {
            logger_1.default.error('Failed to toggle notifications', {
                userId,
                enabled,
                error: error.message,
            });
            throw error;
        }
    }
}
exports.notificationService = new NotificationService();
//# sourceMappingURL=notification.services.js.map