"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userService = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const models_1 = require("../models");
const enums_1 = require("../enums/enums");
const hash_utils_1 = require("../utils/hash_utils");
const jwt_utils_1 = require("../utils/jwt_utils");
const app_errors_1 = require("../errors/app_errors");
const notification_utils_1 = require("../utils/notification_utils");
const sms_services_1 = require("./sms.services");
const logger_1 = __importDefault(require("../config/logger"));
const otp_utils_1 = require("../utils/otp_utils");
const app_constants_1 = require("../constants/app_constants");
const env_config_1 = require("../config/env_config");
class UserServices {
    shouldSkipOtp() {
        return env_config_1.config.server.disableOtpVerification;
    }
    // private utility function to send OTP
    async sendOtp(phone, otp) {
        try {
            // await smsService.sendSms(phone, `Your OTP is ${otp}`, { isOtp: true });
            await sms_services_1.smsService.sendSms(phone, app_constants_1.appConstants.otpMessage(otp), { isOtp: true });
        }
        catch (error) {
            logger_1.default.error('Failed to send OTP SMS', {
                error: error.message,
                phone,
                timestamp: new Date().toISOString(),
            });
            // throw new InternalServerError('Failed to send OTP SMS', {
            // throw new InternalServerError('Internal server error', {
            //   code: 'OTP_SMS_FAILED',
            //   details: { originalError: error.message },
            // });
        }
    }
    async login(phone) {
        const session = await mongoose_1.default.startSession();
        session.startTransaction();
        try {
            logger_1.default.info(`Login attempt for phone: ${phone}`);
            // const { code: otp, expirationTime } = generateOtp();
            // Generate OTP or use dummy value if skipping verification
            const { code: otp, expirationTime } = this.shouldSkipOtp()
                ? { code: '000000', expirationTime: new Date(Date.now() + 10 * 60 * 1000) }
                : (0, otp_utils_1.generateOtp)();
            logger_1.default.info(`Generated OTP: ${otp}, Expires at: ${expirationTime}`);
            // Check if user exists first
            const user = await models_1.User.findOne({ phone }).session(session);
            logger_1.default.info(`Existing user found: ${user ? 'Yes' : 'No'}`);
            if (!user) {
                // Clean up any existing TempOtp for this phone
                await models_1.TempOtp.deleteMany({ phone }).session(session);
                // Store OTP in temporary collection
                const newTempUser = await models_1.TempOtp.create([
                    {
                        phone,
                        code: otp,
                        expirationTime,
                        createdAt: new Date(),
                    },
                ], { session });
                logger_1.default.info(`TempOtp created with ID: ${newTempUser[0]._id}`);
                // Attempt to send SMS
                await this.sendOtp(phone, otp);
                await session.commitTransaction();
                logger_1.default.info('Transaction committed for new user');
                return {
                    isNewUser: true,
                    _id: newTempUser[0]._id,
                    phone: newTempUser[0].phone,
                };
            }
            if (!user.isActive) {
                throw new app_errors_1.UnauthorizedError('Your account has been deactivated');
            }
            logger_1.default.info(`Updating existing user OTP...`);
            await models_1.User.updateOne({ phone }, { $set: { otp: { code: otp, expirationTime } } }, { session });
            // Attempt to send SMS - if this fails, the transaction will roll back
            if (!this.shouldSkipOtp()) {
                await this.sendOtp(phone, otp);
            }
            await session.commitTransaction();
            logger_1.default.info('Transaction committed for existing user');
            return {
                isNewUser: false,
                _id: user._id,
                phone: user.phone,
                email: user.email,
                role: user.role,
                addresses: user.addresses,
                isActive: user.isActive,
                agentMetadata: user.agentMetadata,
            };
        }
        catch (error) {
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Login transaction aborted successfully', {
                        phone,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Critical: Failed to abort login transaction', {
                    error: abortError.message,
                    originalError: error.message,
                    phone,
                    timestamp: new Date().toISOString(),
                });
            }
            logger_1.default.error('Login transaction aborted', {
                phone,
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    async verifyOtp(phone, otp) {
        logger_1.default.info(`Verifying OTP for phone: ${phone}`);
        const now = new Date();
        const session = await mongoose_1.default.startSession();
        session.startTransaction();
        try {
            // STEP 1: Check for new user in TempOtp
            const tempOtp = await models_1.TempOtp.findOne({
                phone,
                code: otp,
                expirationTime: { $gt: now },
            }).session(session);
            if (tempOtp) {
                logger_1.default.info(`Valid TempOtp found. Creating new user.`);
                const newUser = await models_1.User.create([
                    {
                        phone,
                        role: enums_1.UserRole.CUSTOMER,
                        isActive: true,
                        addresses: [],
                    },
                ], { session });
                await models_1.TempOtp.deleteOne({ _id: tempOtp._id }).session(session);
                await session.commitTransaction();
                const token = (0, jwt_utils_1.generateToken)(newUser[0]._id.toString(), newUser[0].role);
                logger_1.default.info(`New user created: ${newUser[0]._id}`);
                return { token, user: newUser[0] };
            }
            // STEP 2: Check existing user
            const user = await models_1.User.findOne({ phone }).session(session);
            if (!user) {
                const expiredTempOtp = await models_1.TempOtp.findOne({ phone }).session(session);
                if (expiredTempOtp) {
                    logger_1.default.warn(`OTP expired for unregistered user`);
                    throw new app_errors_1.UnauthorizedError('OTP has expired. Please request a new one.');
                }
                logger_1.default.warn(`No user found with phone: ${phone}`);
                throw new app_errors_1.NotFoundError('User not found. Please register first.');
            }
            // STEP 3: Validate OTP on existing user
            if (!user.otp || !user.otp.code) {
                logger_1.default.warn(`No OTP set for user: ${user._id}`);
                throw new app_errors_1.UnauthorizedError('OTP not found. Please request a new one.');
            }
            if (user.otp.code !== otp) {
                logger_1.default.warn(`Incorrect OTP entered for user: ${user._id}`);
                throw new app_errors_1.UnauthorizedError('Incorrect OTP. Please try again.');
            }
            if (user.otp.expirationTime <= now) {
                logger_1.default.warn(`Expired OTP entered for user: ${user._id}`);
                throw new app_errors_1.UnauthorizedError('OTP has expired. Please request a new one.');
            }
            await models_1.User.updateOne({ phone }, { $set: { otp: null } }).session(session);
            await session.commitTransaction();
            const token = (0, jwt_utils_1.generateToken)(user._id.toString(), user.role);
            logger_1.default.info(`User verified successfully: ${user._id}`);
            return { token, user };
        }
        catch (error) {
            try {
                if (session.inTransaction()) {
                    await session.abortTransaction();
                    logger_1.default.info('Transaction aborted due to OTP verification failure', {
                        phone,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (abortError) {
                logger_1.default.error('Failed to abort transaction after OTP verification failure', {
                    error: abortError.message,
                    originalError: error.message,
                    phone,
                    timestamp: new Date().toISOString(),
                });
            }
            logger_1.default.error('OTP verification failed', {
                phone,
                otp,
                error: error.message,
                timestamp: new Date().toISOString(),
            });
            throw error;
        }
        finally {
            if (!session.hasEnded) {
                await session.endSession();
            }
        }
    }
    async resendOtp(phone) {
        logger_1.default.info(`Resend OTP requested for phone: ${phone}`);
        const user = await models_1.User.findOne({ phone }).select('otp phone');
        if (!user) {
            logger_1.default.warn(`No user found with phone: ${phone}`);
            throw new app_errors_1.NotFoundError('User not found with this phone number');
        }
        const now = new Date();
        // Check if existing OTP is still valid
        if (user.otp?.expirationTime && user.otp.expirationTime > now) {
            const secondsLeft = Math.ceil((user.otp.expirationTime.getTime() - now.getTime()) / 1000);
            logger_1.default.warn(`OTP already sent for ${phone}, expires in ${secondsLeft}s`);
            throw new app_errors_1.BadRequestError(`OTP already sent. Please wait ${secondsLeft} seconds before requesting a new one.`);
        }
        // Generate a secure 6-digit OTP
        // const { code: otp, expirationTime } = generateOtp();
        // Generate OTP or use dummy value if skipping verification
        const { code: otp, expirationTime } = this.shouldSkipOtp()
            ? { code: '000000', expirationTime: new Date(Date.now() + 10 * 60 * 1000) }
            : (0, otp_utils_1.generateOtp)();
        // const expirationTime = new Date(now.getTime() + 2 * 60 * 1000); // 2 minutes
        // Send OTP via SMS
        if (!this.shouldSkipOtp()) {
            await this.sendOtp(phone, otp);
        }
        // Update user's OTP in one atomic operation
        await models_1.User.updateOne({ phone }, { $set: { 'otp.code': otp, 'otp.expirationTime': expirationTime } });
        // In production, don't return the OTP — only for testing or debugging
        return { message: 'OTP sent successfully' };
    }
    /**
     * Register a new admin or agent user (admin only)
     */
    async registerAdminOrAgent(userData, currentUser) {
        // Verify current user is admin
        if (currentUser.role !== enums_1.UserRole.ADMIN) {
            throw new app_errors_1.UnauthorizedError('Only admins can register new admins or agents');
        }
        // Check if user already exists
        const existingUser = await models_1.User.findOne({ phone: userData.phone });
        if (existingUser) {
            throw new app_errors_1.DuplicateResourceError('User with this phone number already exists');
        }
        // Hash password
        const passwordHash = await (0, hash_utils_1.hashPassword)(userData.password);
        // Prepare user data
        const newUserData = {
            phone: userData.phone,
            email: userData.email,
            passwordHash,
            role: userData.role,
            isActive: true,
        };
        // Add agent metadata if role is agent
        if (userData.role === enums_1.UserRole.AGENT && userData.agentMetadata) {
            newUserData.agentMetadata = {
                vehicle: {
                    type: userData.agentMetadata.vehicle.type,
                    number: userData.agentMetadata.vehicle.number,
                },
                isOnDuty: false,
                lastKnownLocation: {
                    type: 'Point',
                    coordinates: [0, 0], // Default coordinates
                },
                rating: 0,
            };
        }
        // Create new user
        const newUser = await models_1.User.create(newUserData);
        return {
            _id: newUser._id,
            phone: newUser.phone,
            email: newUser.email,
            role: newUser.role,
            isActive: newUser.isActive,
            agentMetadata: newUser.agentMetadata,
        };
    }
    /**
     * Get single user data
     */
    async getSingleUser(userId, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only access their own data unless they're admins
        if (currentUser.role !== enums_1.UserRole.ADMIN && currentUser.userId !== userId.toString()) {
            throw new app_errors_1.UnauthorizedError('You can only access your own user data');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        return {
            _id: user._id,
            phone: user.phone,
            email: user.email,
            role: user.role,
            addresses: user.addresses,
            isActive: user.isActive,
            agentMetadata: user.agentMetadata,
        };
    }
    /**
     * Get all users data (admin only)
     */
    async getAllUsers(currentUser, options = {}) {
        // Verify current user is admin
        // if (currentUser.role !== UserRole.ADMIN) {
        //   throw new UnauthorizedError('Only admins can access all users data');
        // }
        // Build query
        const query = {};
        if (options.role)
            query.role = options.role;
        if (options.isActive !== undefined)
            query.isActive = options.isActive;
        // Pagination
        const page = options.page || 1;
        const limit = options.limit || 10;
        const skip = (page - 1) * limit;
        // Sorting
        const sortOptions = {};
        if (options.sortBy) {
            sortOptions[options.sortBy] = options.sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sortOptions.createdAt = -1; // Default sort by creation date, newest first
        }
        console.log('query is : ', query);
        // Execute query
        const users = await models_1.User.find(query).sort(sortOptions).skip(skip).limit(limit);
        // Get total count for pagination
        const totalUsers = await models_1.User.countDocuments(query);
        return {
            users,
            pagination: {
                total: totalUsers,
                page,
                limit,
                pages: Math.ceil(totalUsers / limit),
            },
        };
    }
    /**
     * Update user data
     */
    async updateUser(userId, updateData, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only update their own data unless they're admins
        const isOwnAccount = currentUser.userId === userId.toString();
        const isAdmin = currentUser.role === enums_1.UserRole.ADMIN;
        if (!isAdmin && !isOwnAccount) {
            throw new app_errors_1.UnauthorizedError('You can only update your own user data');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Prepare update data
        const updateFields = {};
        // Basic fields
        if (updateData.email)
            updateFields.email = updateData.email;
        // Password update
        if (updateData.password) {
            updateFields.passwordHash = await (0, hash_utils_1.hashPassword)(updateData.password);
        }
        // Addresses update
        if (updateData.addresses && updateData.addresses.length > 0) {
            updateFields.addresses = updateData.addresses.map(addr => ({
                tag: addr.tag || 'home',
                location: {
                    type: 'Point',
                    coordinates: addr.coordinates,
                },
                details: addr.details,
                contactPhone: addr.contactPhone || user.phone,
            }));
        }
        // Active status (admin only)
        if (isAdmin && updateData.isActive !== undefined) {
            updateFields.isActive = updateData.isActive;
        }
        // Agent metadata updates
        if (user.role === enums_1.UserRole.AGENT && updateData.agentMetadata) {
            // Vehicle updates (admin only)
            if (isAdmin && updateData.agentMetadata.vehicle) {
                updateFields['agentMetadata.vehicle'] = {
                    ...user.agentMetadata?.vehicle,
                    ...updateData.agentMetadata.vehicle,
                };
            }
            // Duty status (can be updated by the agent or admin)
            if (updateData.agentMetadata.isOnDuty !== undefined) {
                updateFields['agentMetadata.isOnDuty'] = updateData.agentMetadata.isOnDuty;
            }
            // Location updates (can be updated by the agent or admin)
            if (updateData.agentMetadata.lastKnownLocation) {
                updateFields['agentMetadata.lastKnownLocation'] = {
                    type: 'Point',
                    coordinates: updateData.agentMetadata.lastKnownLocation.coordinates,
                };
            }
        }
        // Update user
        const updatedUser = await models_1.User.findByIdAndUpdate(userObjectId, { $set: updateFields }, { new: true }).select('-passwordHash');
        if (!updatedUser) {
            throw new app_errors_1.NotFoundError('User not found after update');
        }
        return updatedUser;
    }
    /**
     * Delete user (admin only or self-delete)
     */
    async deleteUser(userId, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only delete their own account unless they're admins
        const isOwnAccount = currentUser.userId === userId.toString();
        const isAdmin = currentUser.role === enums_1.UserRole.ADMIN;
        if (!isAdmin && !isOwnAccount) {
            throw new app_errors_1.UnauthorizedError('You can only delete your own account');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Prevent deleting the last admin
        if (user.role === enums_1.UserRole.ADMIN) {
            const adminCount = await models_1.User.countDocuments({ role: enums_1.UserRole.ADMIN });
            if (adminCount <= 1) {
                throw new app_errors_1.ValidationError('Cannot delete the last admin account');
            }
        }
        // Delete user
        await models_1.User.findByIdAndDelete(userObjectId);
        return { success: true, message: 'User deleted successfully' };
    }
    /**
     * Subscribe user to notification topic
     */
    async subscribeToTopic(userId, topic, deviceToken, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only subscribe themselves unless they're admins
        const isOwnAccount = currentUser.userId === userId.toString();
        const isAdmin = currentUser.role === enums_1.UserRole.ADMIN;
        if (!isAdmin && !isOwnAccount) {
            throw new app_errors_1.UnauthorizedError('You can only manage your own subscriptions');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Subscribe to topic
        const result = await (0, notification_utils_1.subscribeToTopic)([deviceToken], topic);
        return {
            success: result.successCount > 0,
            topic,
            failureCount: result.failureCount,
            successCount: result.successCount,
        };
    }
    /**
     * Unsubscribe user from notification topic
     */
    async unsubscribeFromTopic(userId, topic, deviceToken, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only unsubscribe themselves unless they're admins
        const isOwnAccount = currentUser.userId === userId.toString();
        const isAdmin = currentUser.role === enums_1.UserRole.ADMIN;
        if (!isAdmin && !isOwnAccount) {
            throw new app_errors_1.UnauthorizedError('You can only manage your own subscriptions');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Unsubscribe from topic
        const result = await (0, notification_utils_1.unsubscribeFromTopic)([deviceToken], topic);
        return {
            success: result.successCount > 0,
            topic,
            failureCount: result.failureCount,
            successCount: result.successCount,
        };
    }
    /**
     * Toggle user active status (admin only)
     */
    async toggleUserActiveStatus(userId, isActive, currentUser) {
        // Verify current user is admin
        if (currentUser.role !== enums_1.UserRole.ADMIN) {
            throw new app_errors_1.UnauthorizedError('Only admins can toggle user active status');
        }
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Prevent deactivating the last admin
        if (user.role === enums_1.UserRole.ADMIN && !isActive) {
            const adminCount = await models_1.User.countDocuments({ role: enums_1.UserRole.ADMIN, isActive: true });
            if (adminCount <= 1) {
                throw new app_errors_1.ValidationError('Cannot deactivate the last admin account');
            }
        }
        // Update user
        const updatedUser = await models_1.User.findByIdAndUpdate(userObjectId, { $set: { isActive } }, { new: true });
        if (!updatedUser) {
            throw new app_errors_1.NotFoundError('User not found after update');
        }
        return updatedUser;
    }
    /**
     * Toggle agent on duty status (admin or agent only)
     */
    async toggleAgentOnDutyStatus(userId, isOnDuty, currentUser) {
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Check permissions - users can only toggle their own status unless they're admins
        const isOwnAccount = currentUser.userId === userId.toString();
        const isAdmin = currentUser.role === enums_1.UserRole.ADMIN;
        if (!isAdmin && !isOwnAccount) {
            throw new app_errors_1.UnauthorizedError('You can only toggle your own on duty status');
        }
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Update user
        const updatedUser = await models_1.User.findByIdAndUpdate(userObjectId, { $set: { 'agentMetadata.isOnDuty': isOnDuty } }, { new: true });
        if (!updatedUser) {
            throw new app_errors_1.NotFoundError('User not found after update');
        }
        return updatedUser;
    }
    /**
     * Change user role (admin only)
     */
    async changeUserRole(userId, newRole, currentUser) {
        // Verify current user is admin
        if (currentUser.role !== enums_1.UserRole.ADMIN) {
            throw new app_errors_1.UnauthorizedError('Only admins can change user roles');
        }
        // Convert string ID to ObjectId if needed
        const userObjectId = typeof userId === 'string' ? new mongoose_1.Types.ObjectId(userId) : userId;
        // Find user
        const user = await models_1.User.findById(userObjectId);
        if (!user) {
            throw new app_errors_1.NotFoundError('User not found');
        }
        // Prevent changing the role of the last admin
        if (user.role === enums_1.UserRole.ADMIN && newRole !== enums_1.UserRole.ADMIN) {
            const adminCount = await models_1.User.countDocuments({ role: enums_1.UserRole.ADMIN, isActive: true });
            if (adminCount <= 1) {
                throw new app_errors_1.ValidationError('Cannot change the role of the last admin account');
            }
        }
        // Prevent changing own role
        if (currentUser.userId === userId.toString()) {
            throw new app_errors_1.ValidationError('You cannot change your own role');
        }
        // Prepare update data
        const updateData = { role: newRole };
        // Handle role-specific metadata
        if (newRole === enums_1.UserRole.AGENT && user.role !== enums_1.UserRole.AGENT) {
            // Initialize agent metadata if changing to agent role
            updateData.agentMetadata = {
                vehicle: {
                    type: enums_1.VehicleType.MOTORCYCLE, // Default vehicle type
                    number: '', // Will need to be updated later
                },
                isOnDuty: false,
                lastKnownLocation: {
                    type: 'Point',
                    coordinates: [0, 0], // Default coordinates
                },
                rating: 0,
            };
        }
        else if (newRole !== enums_1.UserRole.AGENT && user.role === enums_1.UserRole.AGENT) {
            // Remove agent metadata if changing from agent role
            updateData.$unset = { agentMetadata: 1 };
        }
        // Update user
        const updatedUser = await models_1.User.findByIdAndUpdate(userObjectId, updateData, { new: true });
        if (!updatedUser) {
            throw new app_errors_1.NotFoundError('User not found after update');
        }
        logger_1.default.info(`User role changed: ${user.phone} from ${user.role} to ${newRole} by admin ${currentUser.userId}`);
        return updatedUser;
    }
}
// singleton user services export
exports.userService = new UserServices();
//# sourceMappingURL=user.service.js.map