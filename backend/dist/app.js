"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_2 = require("express");
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const error_handler_middle_ware_1 = require("./middleware/error_handler_middle_ware");
const env_config_1 = require("./config/env_config");
const cors_config_1 = require("./config/cors_config");
const response_1 = require("./utils/response");
const routes_1 = __importDefault(require("./routes"));
const app_errors_1 = require("./errors/app_errors");
const logger_interceptor_middleware_1 = require("./middleware/logger_interceptor_middleware");
const app = (0, express_1.default)();
// Log CORS configuration on startup
(0, cors_config_1.logCorsConfig)();
// middleware
app.use((0, express_2.json)());
app.use((0, express_2.urlencoded)({ extended: true }));
// security
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: 'cross-origin' }, // Allow cross-origin requests for images
}));
// CORS - Configuration imported from config/cors_config.ts
// The CORS middleware automatically handles preflight OPTIONS requests
app.use((0, cors_1.default)(cors_config_1.corsOptions));
/// Logger Interceptor for request and response
app.use(logger_interceptor_middleware_1.loggerInterceptor);
// routes
app.use(routes_1.default);
// test api
app.get('/', (_req, res) => {
    (0, response_1.sendResponse)(res, 200, 'success', 'Welcome to Gas System Project Backend');
    return;
});
// /health
app.get('/health', (_req, res) => {
    (0, response_1.sendResponse)(res, 200, 'success', 'Service is up and running');
    return;
});
// CORS test endpoint
app.get('/cors-test', (_req, res) => {
    const corsTestData = {
        message: 'CORS is working correctly!',
        timestamp: new Date().toISOString(),
        environment: env_config_1.config.server.env,
        corsConfig: {
            allowedOrigins: [...env_config_1.config.server.corsOrigins, env_config_1.config.server.frontendUrl, env_config_1.config.server.url],
            credentials: cors_config_1.corsOptions.credentials,
            methods: cors_config_1.corsOptions.methods,
        },
    };
    res.status(200).json({
        status: 'success',
        data: corsTestData,
    });
    return;
});
// 404 handler
app.use((req, _res, next) => {
    next(new app_errors_1.NotFoundError(`Route not found: ${req.method} ${req.originalUrl}`));
});
// error handler
app.use(error_handler_middle_ware_1.globalErrorHandler);
exports.default = app;
//# sourceMappingURL=app.js.map