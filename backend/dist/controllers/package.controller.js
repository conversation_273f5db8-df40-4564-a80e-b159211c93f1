"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.packageController = void 0;
const package_services_1 = require("../services/package.services");
const response_1 = require("../utils/response");
const app_errors_1 = require("../errors/app_errors");
const mongoose_1 = require("mongoose");
const logger_1 = __importDefault(require("../config/logger"));
const image_upload_service_1 = require("../services/image-upload.service");
class PackageController {
    /**
     * @route   POST /api/v1/packages
     * @desc    Create a new package
     * @access  Private (Admin only)
     */
    async createPackage(req, res, next) {
        try {
            const { name, description, cylinder, includedSpareParts, totalPrice, costPrice, discount, imageUrl, quantity, minimumStockLevel, } = req.body;
            // Handle uploaded image file (if present)
            const imageFile = req.file;
            let imagePath;
            if (imageFile) {
                // Process uploaded image
                imagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(imageFile, 'packages');
                logger_1.default.info(`Image uploaded for package: ${imagePath}`);
            }
            // Validation
            if (!name || !cylinder || !includedSpareParts) {
                throw new app_errors_1.ValidationError('Name, cylinder, and included spare parts are required');
            }
            if (!mongoose_1.Types.ObjectId.isValid(cylinder)) {
                throw new app_errors_1.ValidationError('Invalid cylinder ID');
            }
            if (!Array.isArray(includedSpareParts) || includedSpareParts.length === 0) {
                throw new app_errors_1.ValidationError('At least one spare part must be included');
            }
            // Validate spare parts structure
            for (const item of includedSpareParts) {
                if (!item.part || !mongoose_1.Types.ObjectId.isValid(item.part)) {
                    throw new app_errors_1.ValidationError('Invalid spare part ID in included parts');
                }
                if (!item.quantity || item.quantity <= 0) {
                    throw new app_errors_1.ValidationError('Spare part quantity must be positive');
                }
            }
            if (totalPrice !== undefined && totalPrice <= 0) {
                throw new app_errors_1.ValidationError('Total price must be positive');
            }
            if (costPrice !== undefined && costPrice <= 0) {
                throw new app_errors_1.ValidationError('Cost price must be positive');
            }
            if (discount !== undefined && (discount < 0 || discount > 100)) {
                throw new app_errors_1.ValidationError('Discount must be between 0 and 100');
            }
            const packageData = {
                name: name.trim(),
                description: description?.trim(),
                cylinder: new mongoose_1.Types.ObjectId(cylinder),
                includedSpareParts,
                totalPrice: totalPrice ? Number(totalPrice) : undefined,
                costPrice: costPrice ? Number(costPrice) : undefined,
                discount: discount ? Number(discount) : undefined,
                imageUrl: imageUrl?.trim(), // Legacy field for backward compatibility
                imagePath, // New field for uploaded images
                quantity: quantity ? Number(quantity) : undefined,
                minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
            };
            const newPackage = await package_services_1.packageService.createPackage(packageData);
            logger_1.default.info('Package created successfully', {
                packageId: newPackage._id,
                name: newPackage.name,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 201, 'success', 'Package created successfully', {
                data: newPackage,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages
     * @desc    Get all packages with filtering and pagination
     * @access  Private (All authenticated users)
     */
    async getPackages(req, res, next) {
        try {
            const { search, cylinder, isActive, minPrice, maxPrice, page = 1, limit = 10, populate = 'true', } = req.query;
            // Validation
            const pageNum = Math.max(1, parseInt(page) || 1);
            const limitNum = Math.min(50, Math.max(1, parseInt(limit) || 10));
            const shouldPopulate = populate === 'true';
            const filters = {};
            if (search)
                filters.search = search;
            if (cylinder && mongoose_1.Types.ObjectId.isValid(cylinder)) {
                filters.cylinder = new mongoose_1.Types.ObjectId(cylinder);
            }
            if (isActive !== undefined)
                filters.isActive = isActive === 'true';
            if (minPrice)
                filters.minPrice = Number(minPrice);
            if (maxPrice)
                filters.maxPrice = Number(maxPrice);
            const result = await package_services_1.packageService.listPackages(filters, { page: pageNum, limit: limitNum }, shouldPopulate);
            (0, response_1.sendResponse)(res, 200, 'success', 'Packages retrieved successfully', {
                data: result.data,
                meta: {
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: result.total,
                        pages: Math.ceil(result.total / limitNum),
                    },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/:id
     * @desc    Get package by ID
     * @access  Private (All authenticated users)
     */
    async getPackageById(req, res, next) {
        try {
            const { id } = req.params;
            const { populate = 'true' } = req.query;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const shouldPopulate = populate === 'true';
            const packageDoc = await package_services_1.packageService.getPackageById(new mongoose_1.Types.ObjectId(id), shouldPopulate);
            (0, response_1.sendResponse)(res, 200, 'success', 'Package retrieved successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/packages/:id
     * @desc    Update package details
     * @access  Private (Admin only)
     */
    async updatePackage(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            // Handle uploaded image file (if present)
            const imageFile = req.file;
            let imagePath;
            if (imageFile) {
                // Process uploaded image
                imagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(imageFile, 'packages');
                logger_1.default.info(`Image uploaded for package update: ${imagePath}`);
                updateData.imagePath = imagePath;
            }
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            // Validate numeric fields if provided
            if (updateData.totalPrice !== undefined && updateData.totalPrice <= 0) {
                throw new app_errors_1.ValidationError('Total price must be positive');
            }
            if (updateData.costPrice !== undefined && updateData.costPrice <= 0) {
                throw new app_errors_1.ValidationError('Cost price must be positive');
            }
            if (updateData.discount !== undefined &&
                (updateData.discount < 0 || updateData.discount > 100)) {
                throw new app_errors_1.ValidationError('Discount must be between 0 and 100');
            }
            // Validate spare parts if provided
            if (updateData.includedSpareParts) {
                if (!Array.isArray(updateData.includedSpareParts) ||
                    updateData.includedSpareParts.length === 0) {
                    throw new app_errors_1.ValidationError('At least one spare part must be included');
                }
                for (const item of updateData.includedSpareParts) {
                    if (!item.part || !mongoose_1.Types.ObjectId.isValid(item.part)) {
                        throw new app_errors_1.ValidationError('Invalid spare part ID in included parts');
                    }
                    if (!item.quantity || item.quantity <= 0) {
                        throw new app_errors_1.ValidationError('Spare part quantity must be positive');
                    }
                }
            }
            const packageDoc = await package_services_1.packageService.updatePackage(new mongoose_1.Types.ObjectId(id), updateData);
            logger_1.default.info('Package updated successfully', {
                packageId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package updated successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/packages/:id/toggle-status
     * @desc    Toggle package active status
     * @access  Private (Admin only)
     */
    async togglePackageStatus(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const packageDoc = await package_services_1.packageService.togglePackageStatus(new mongoose_1.Types.ObjectId(id));
            logger_1.default.info('Package status toggled successfully', {
                packageId: id,
                newStatus: packageDoc.isActive,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package status updated successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/analytics
     * @desc    Get package analytics
     * @access  Private (Admin only)
     */
    async getPackageAnalytics(_req, res, next) {
        try {
            const analytics = await package_services_1.packageService.getPackageAnalytics();
            (0, response_1.sendResponse)(res, 200, 'success', 'Package analytics retrieved successfully', {
                data: analytics,
            });
        }
        catch (error) {
            next(error);
        }
    }
    // ==================== ADMINISTRATIVE INVENTORY METHODS ====================
    // Note: Reservation, release, and sales operations are now handled automatically
    // through the order lifecycle. Only administrative operations remain.
    /**
     * @route   POST /api/v1/packages/:id/restock
     * @desc    Restock package
     * @access  Private (Admin only)
     */
    async restockPackage(req, res, next) {
        try {
            const { id } = req.params;
            const { quantity } = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            if (!quantity || quantity <= 0) {
                throw new app_errors_1.ValidationError('Quantity must be a positive number');
            }
            const packageDoc = await package_services_1.packageService.restockPackage(id, Number(quantity));
            logger_1.default.info('Package restocked successfully', {
                packageId: id,
                quantity: Number(quantity),
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package restocked successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/packages/:id/adjust-stock
     * @desc    Adjust package stock
     * @access  Private (Admin only)
     */
    async adjustPackageStock(req, res, next) {
        try {
            const { id } = req.params;
            const { adjustment, reason } = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            if (adjustment === undefined || adjustment === 0) {
                throw new app_errors_1.ValidationError('Adjustment must be a non-zero number');
            }
            if (!reason || typeof reason !== 'string') {
                throw new app_errors_1.ValidationError('Reason is required');
            }
            const packageDoc = await package_services_1.packageService.adjustPackageStock(id, Number(adjustment), reason.trim());
            logger_1.default.info('Package stock adjusted successfully', {
                packageId: id,
                adjustment: Number(adjustment),
                reason: reason.trim(),
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package stock adjusted successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/low-stock
     * @desc    Get packages with low stock
     * @access  Private (Admin, Agent)
     */
    async getLowStockPackages(_req, res, next) {
        try {
            const packages = await package_services_1.packageService.getLowStockPackages();
            (0, response_1.sendResponse)(res, 200, 'success', 'Low stock packages retrieved successfully', {
                data: packages,
                meta: { count: packages.length },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/:id/availability
     * @desc    Check package availability
     * @access  Private (All authenticated users)
     */
    async checkPackageAvailability(req, res, next) {
        try {
            const { id } = req.params;
            const { quantity = 1 } = req.query;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const requestedQuantity = Math.max(1, parseInt(quantity) || 1);
            const availability = await package_services_1.packageService.checkPackageAvailability(id, requestedQuantity);
            (0, response_1.sendResponse)(res, 200, 'success', 'Package availability checked successfully', {
                data: availability,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/:id/available-quantity
     * @desc    Get available package quantity
     * @access  Private (All authenticated users)
     */
    async getAvailableQuantity(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const availableQuantity = await package_services_1.packageService.getAvailablePackageQuantity(id);
            (0, response_1.sendResponse)(res, 200, 'success', 'Available quantity retrieved successfully', {
                data: { availableQuantity },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/sales-statistics
     * @desc    Get package sales statistics
     * @access  Private (Admin only)
     */
    async getPackageSalesStatistics(_req, res, next) {
        try {
            const statistics = await package_services_1.packageService.getPackageSalesStatistics();
            (0, response_1.sendResponse)(res, 200, 'success', 'Package sales statistics retrieved successfully', {
                data: statistics,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/packages/bulk-status
     * @desc    Bulk update package statuses
     * @access  Private (Admin only)
     */
    async bulkUpdatePackageStatus(req, res, next) {
        try {
            const { packageIds, isActive } = req.body;
            if (!Array.isArray(packageIds) || packageIds.length === 0) {
                throw new app_errors_1.ValidationError('Package IDs array is required');
            }
            if (typeof isActive !== 'boolean') {
                throw new app_errors_1.ValidationError('isActive must be a boolean');
            }
            // Validate all package IDs
            for (const id of packageIds) {
                if (!mongoose_1.Types.ObjectId.isValid(id)) {
                    throw new app_errors_1.ValidationError(`Invalid package ID: ${id}`);
                }
            }
            const modifiedCount = await package_services_1.packageService.bulkUpdatePackageStatus(packageIds, isActive);
            logger_1.default.info('Bulk package status update completed', {
                packageIds,
                isActive,
                modifiedCount,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package statuses updated successfully', {
                data: { modifiedCount },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   DELETE /api/v1/packages/:id
     * @desc    Soft delete a package
     * @access  Private (Admin only)
     */
    async deletePackage(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const packageDoc = await package_services_1.packageService.deletePackage(id, req.user?.userId.toString());
            logger_1.default.info('Package soft deleted successfully', {
                packageId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package deleted successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/packages/:id/restore
     * @desc    Restore a soft-deleted package
     * @access  Private (Admin only)
     */
    async restorePackage(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const packageDoc = await package_services_1.packageService.restorePackage(id);
            logger_1.default.info('Package restored successfully', {
                packageId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package restored successfully', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   DELETE /api/v1/packages/:id/permanent
     * @desc    Permanently delete a package (hard delete)
     * @access  Private (Admin only)
     */
    async permanentlyDeletePackage(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid package ID');
            }
            const packageDoc = await package_services_1.packageService.permanentlyDeletePackage(id);
            logger_1.default.info('Package permanently deleted', {
                packageId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Package permanently deleted', {
                data: packageDoc,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/packages/deleted
     * @desc    Get soft-deleted packages
     * @access  Private (Admin only)
     */
    async getDeletedPackages(req, res, next) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const result = await package_services_1.packageService.listDeletedPackages({ page, limit });
            (0, response_1.sendResponse)(res, 200, 'success', 'Deleted packages retrieved successfully', {
                data: result.data,
                meta: {
                    pagination: {
                        page,
                        limit,
                        total: result.total,
                        pages: Math.ceil(result.total / limit),
                    },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.packageController = new PackageController();
//# sourceMappingURL=package.controller.js.map