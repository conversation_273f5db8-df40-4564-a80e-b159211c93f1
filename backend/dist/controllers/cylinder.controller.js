"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cylinderController = void 0;
const cylinder_services_1 = require("../services/cylinder.services");
const response_1 = require("../utils/response");
const app_errors_1 = require("../errors/app_errors");
const enums_1 = require("../enums/enums");
const mongoose_1 = require("mongoose");
const logger_1 = __importDefault(require("../config/logger"));
const image_upload_service_1 = require("../services/image-upload.service");
class CylinderController {
    /**
     * @route   POST /api/v1/cylinders
     * @desc    Create a new cylinder type
     * @access  Private (Admin only)
     */
    async createCylinder(req, res, next) {
        try {
            const { type, material, price, cost, imageUrl, description, quantity, minimumStockLevel, status, } = req.body;
            // Handle uploaded image file (if present)
            const imageFile = req.file;
            let imagePath;
            if (imageFile) {
                // Process uploaded image
                imagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(imageFile, 'cylinders');
                logger_1.default.info(`Image uploaded for cylinder: ${imagePath}`);
            }
            // Validation
            if (!type || !material || !price || !cost) {
                throw new app_errors_1.ValidationError('Type, material, price, and cost are required');
            }
            if (!Object.values(enums_1.CylinderType).includes(type)) {
                throw new app_errors_1.ValidationError('Invalid cylinder type');
            }
            if (!Object.values(enums_1.CylinderMaterial).includes(material)) {
                throw new app_errors_1.ValidationError('Invalid cylinder material');
            }
            if (price <= 0 || cost <= 0) {
                throw new app_errors_1.ValidationError('Price and cost must be positive numbers');
            }
            if (status && !Object.values(enums_1.CylinderStatus).includes(status)) {
                throw new app_errors_1.ValidationError('Invalid cylinder status');
            }
            const cylinderData = {
                type,
                material,
                price: Number(price),
                cost: Number(cost),
                imageUrl: imageUrl?.trim(), // Legacy field for backward compatibility
                imagePath, // New field for uploaded images
                description: description?.trim(),
                quantity: quantity ? Number(quantity) : undefined,
                minimumStockLevel: minimumStockLevel ? Number(minimumStockLevel) : undefined,
                status,
            };
            const cylinder = await cylinder_services_1.cylinderService.createCylinder(cylinderData);
            logger_1.default.info('Cylinder created successfully', {
                cylinderId: cylinder._id,
                type: cylinder.type,
                material: cylinder.material,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 201, 'success', 'Cylinder created successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders
     * @desc    Get all cylinders with filtering and pagination
     * @access  Private (All authenticated users)
     */
    async getCylinders(req, res, next) {
        try {
            const { type, material, status, lowStockOnly, page = 1, limit = 10 } = req.query;
            // Validation
            const pageNum = Math.max(1, parseInt(page) || 1);
            const limitNum = Math.min(50, Math.max(1, parseInt(limit) || 10));
            const filters = {};
            if (type)
                filters.type = type;
            if (material)
                filters.material = material;
            if (status)
                filters.status = status;
            if (lowStockOnly === 'true')
                filters.lowStockOnly = true;
            const result = await cylinder_services_1.cylinderService.listCylinders(filters, {
                page: pageNum,
                limit: limitNum,
            }, {
                userId: req.user?.userId.toString(),
                role: req.user?.role,
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinders retrieved successfully', {
                data: result.data,
                meta: {
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: result.total,
                        pages: Math.ceil(result.total / limitNum),
                    },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders/:id
     * @desc    Get cylinder by ID
     * @access  Private (All authenticated users)
     */
    async getCylinderById(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            const cylinder = await cylinder_services_1.cylinderService.getCylinderById(id);
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder retrieved successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/cylinders/:id
     * @desc    Update cylinder details
     * @access  Private (Admin only)
     */
    async updateCylinder(req, res, next) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            // Handle uploaded image file (if present)
            const imageFile = req.file;
            let imagePath;
            if (imageFile) {
                // Process uploaded image
                imagePath = await image_upload_service_1.ImageUploadService.processUploadedFile(imageFile, 'cylinders');
                logger_1.default.info(`Image uploaded for cylinder update: ${imagePath}`);
                updateData.imagePath = imagePath;
            }
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            // Validate numeric fields if provided
            if (updateData.price !== undefined && updateData.price <= 0) {
                throw new app_errors_1.ValidationError('Price must be positive');
            }
            if (updateData.cost !== undefined && updateData.cost <= 0) {
                throw new app_errors_1.ValidationError('Cost must be positive');
            }
            // Validate enum fields if provided
            if (updateData.type && !Object.values(enums_1.CylinderType).includes(updateData.type)) {
                throw new app_errors_1.ValidationError('Invalid cylinder type');
            }
            if (updateData.material && !Object.values(enums_1.CylinderMaterial).includes(updateData.material)) {
                throw new app_errors_1.ValidationError('Invalid cylinder material');
            }
            if (updateData.status && !Object.values(enums_1.CylinderStatus).includes(updateData.status)) {
                throw new app_errors_1.ValidationError('Invalid cylinder status');
            }
            const cylinder = await cylinder_services_1.cylinderService.updateCylinder(id, updateData);
            logger_1.default.info('Cylinder updated successfully', {
                cylinderId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder updated successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   DELETE /api/v1/cylinders/:id
     * @desc    Soft delete a cylinder type
     * @access  Private (Admin only)
     */
    async deleteCylinder(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            const cylinder = await cylinder_services_1.cylinderService.deleteCylinder(id, req.user?.userId.toString());
            logger_1.default.info('Cylinder soft deleted successfully', {
                cylinderId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder deleted successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   POST /api/v1/cylinders/:id/restore
     * @desc    Restore a soft-deleted cylinder
     * @access  Private (Admin only)
     */
    async restoreCylinder(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            const cylinder = await cylinder_services_1.cylinderService.restoreCylinder(id);
            logger_1.default.info('Cylinder restored successfully', {
                cylinderId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder restored successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   DELETE /api/v1/cylinders/:id/permanent
     * @desc    Permanently delete a cylinder (hard delete)
     * @access  Private (Admin only)
     */
    async permanentlyDeleteCylinder(req, res, next) {
        try {
            const { id } = req.params;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            const cylinder = await cylinder_services_1.cylinderService.permanentlyDeleteCylinder(id);
            logger_1.default.info('Cylinder permanently deleted', {
                cylinderId: id,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder permanently deleted', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders/deleted
     * @desc    Get soft-deleted cylinders
     * @access  Private (Admin only)
     */
    async getDeletedCylinders(req, res, next) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const result = await cylinder_services_1.cylinderService.listDeletedCylinders({ page, limit });
            (0, response_1.sendResponse)(res, 200, 'success', 'Deleted cylinders retrieved successfully', {
                data: result.data,
                meta: {
                    pagination: {
                        page,
                        limit,
                        total: result.total,
                        pages: Math.ceil(result.total / limit),
                    },
                },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders/:id/availability
     * @desc    Check cylinder availability
     * @access  Private (All authenticated users)
     */
    async checkCylinderAvailability(req, res, next) {
        try {
            const { id } = req.params;
            const { quantity = 1 } = req.query;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            const requestedQuantity = Math.max(1, parseInt(quantity) || 1);
            const availability = await cylinder_services_1.cylinderService.checkAvailability(id, requestedQuantity);
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder availability checked successfully', {
                data: availability,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders/low-stock
     * @desc    Get cylinders with low stock
     * @access  Private (Admin, Agent)
     */
    async getLowStockCylinders(_req, res, next) {
        try {
            const cylinders = await cylinder_services_1.cylinderService.getLowStockAlerts();
            (0, response_1.sendResponse)(res, 200, 'success', 'Low stock cylinders retrieved successfully', {
                data: cylinders,
                meta: { count: cylinders.length },
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   GET /api/v1/cylinders/statistics
     * @desc    Get cylinder sales statistics
     * @access  Private (Admin only)
     */
    async getCylinderStatistics(_req, res, next) {
        try {
            const statistics = await cylinder_services_1.cylinderService.getSalesStatistics();
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder statistics retrieved successfully', {
                data: statistics,
            });
        }
        catch (error) {
            next(error);
        }
    }
    // ==================== ADMINISTRATIVE INVENTORY METHODS ====================
    // Note: Reservation, release, and sales operations are now handled automatically
    // through the order lifecycle. Only administrative operations remain.
    /**
     * @route   POST /api/v1/cylinders/:id/restock
     * @desc    Restock cylinder
     * @access  Private (Admin only)
     */
    async restockCylinder(req, res, next) {
        try {
            const { id } = req.params;
            const { quantity } = req.body;
            if (!mongoose_1.Types.ObjectId.isValid(id)) {
                throw new app_errors_1.BadRequestError('Invalid cylinder ID');
            }
            if (!quantity || quantity <= 0) {
                throw new app_errors_1.ValidationError('Quantity must be a positive number');
            }
            const cylinder = await cylinder_services_1.cylinderService.restock(id, Number(quantity));
            logger_1.default.info('Cylinder restocked successfully', {
                cylinderId: id,
                quantity: Number(quantity),
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder restocked successfully', {
                data: cylinder,
            });
        }
        catch (error) {
            next(error);
        }
    }
    /**
     * @route   PUT /api/v1/cylinders/bulk-status
     * @desc    Bulk update cylinder statuses
     * @access  Private (Admin only)
     */
    async bulkUpdateCylinderStatus(req, res, next) {
        try {
            const { cylinderIds, status } = req.body;
            if (!Array.isArray(cylinderIds) || cylinderIds.length === 0) {
                throw new app_errors_1.ValidationError('Cylinder IDs array is required');
            }
            if (!status || !Object.values(enums_1.CylinderStatus).includes(status)) {
                throw new app_errors_1.ValidationError('Valid status is required');
            }
            // Validate all cylinder IDs
            for (const id of cylinderIds) {
                if (!mongoose_1.Types.ObjectId.isValid(id)) {
                    throw new app_errors_1.ValidationError(`Invalid cylinder ID: ${id}`);
                }
            }
            const modifiedCount = await cylinder_services_1.cylinderService.bulkUpdateStatus(cylinderIds, status);
            logger_1.default.info('Bulk cylinder status update completed', {
                cylinderIds,
                status,
                modifiedCount,
                userId: req.user?.userId,
                timestamp: new Date().toISOString(),
            });
            (0, response_1.sendResponse)(res, 200, 'success', 'Cylinder statuses updated successfully', {
                data: { modifiedCount },
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.cylinderController = new CylinderController();
//# sourceMappingURL=cylinder.controller.js.map