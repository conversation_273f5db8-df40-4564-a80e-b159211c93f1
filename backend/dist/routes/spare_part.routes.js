"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const spare_part_controller_1 = require("../controllers/spare_part.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const role_middleware_1 = require("../middleware/role.middleware");
const enums_1 = require("../enums/enums");
const image_upload_service_1 = require("../services/image-upload.service");
const sparePartRouter = (0, express_1.Router)();
// Apply authentication middleware to all spare part routes
sparePartRouter.use(auth_middleware_1.authenticate);
// ==================== CRUD OPERATIONS ====================
/**
 * @route   POST /api/v1/spare-parts
 * @desc    Create a new spare part
 * @access  Private (Admin only)
 * @body    { name, description?, price, cost, category, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
sparePartRouter.post('/', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, spare_part_controller_1.sparePartController.createSparePart);
/**
 * @route   GET /api/v1/spare-parts
 * @desc    Get all spare parts with filtering and pagination
 * @access  Private (All authenticated users)
 * @query   { search?, category?, status?, compatibleWith?, lowStock?, page?, limit? }
 */
sparePartRouter.get('/', spare_part_controller_1.sparePartController.getSpareParts);
/**
 * @route   GET /api/v1/spare-parts/search
 * @desc    Search spare parts
 * @access  Private (All authenticated users)
 * @query   { q, limit? }
 */
sparePartRouter.get('/search', spare_part_controller_1.sparePartController.searchSpareParts);
/**
 * @route   GET /api/v1/spare-parts/low-stock
 * @desc    Get spare parts with low stock
 * @access  Private (Admin, Agent)
 */
sparePartRouter.get('/low-stock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN, enums_1.UserRole.AGENT]), spare_part_controller_1.sparePartController.getLowStockSpareParts);
/**
 * @route   GET /api/v1/spare-parts/statistics
 * @desc    Get spare parts sales statistics
 * @access  Private (Admin only)
 */
sparePartRouter.get('/statistics', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.getSparePartStatistics);
/**
 * @route   GET /api/v1/spare-parts/deleted
 * @desc    Get soft-deleted spare parts
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
sparePartRouter.get('/deleted', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.getDeletedSpareParts);
/**
 * @route   GET /api/v1/spare-parts/:id
 * @desc    Get spare part by ID
 * @access  Private (All authenticated users)
 */
sparePartRouter.get('/:id', spare_part_controller_1.sparePartController.getSparePartById);
/**
 * @route   DELETE /api/v1/spare-parts/:id
 * @desc    Soft delete a spare part
 * @access  Private (Admin only)
 */
sparePartRouter.delete('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.deleteSparePart);
/**
 * @route   POST /api/v1/spare-parts/:id/restore
 * @desc    Restore a soft-deleted spare part
 * @access  Private (Admin only)
 */
sparePartRouter.post('/:id/restore', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.restoreSparePart);
/**
 * @route   DELETE /api/v1/spare-parts/:id/permanent
 * @desc    Permanently delete a spare part (hard delete)
 * @access  Private (Admin only)
 */
sparePartRouter.delete('/:id/permanent', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.permanentlyDeleteSparePart);
/**
 * @route   PUT /api/v1/spare-parts/:id
 * @desc    Update spare part details
 * @access  Private (Admin only)
 * @body    { name?, description?, price?, cost?, category?, compatibleCylinderTypes?, barcode?, minimumStockLevel?, imageUrl? }
 * @file    image (optional multipart/form-data)
 */
sparePartRouter.put('/:id', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), image_upload_service_1.uploadSingleImage, spare_part_controller_1.sparePartController.updateSparePart);
// ==================== ADMINISTRATIVE INVENTORY MANAGEMENT ====================
// Note: Reservation, release, and sales operations are now handled automatically
// through the order lifecycle. Only administrative operations remain.
/**
 * @route   POST /api/v1/spare-parts/:id/restock
 * @desc    Restock spare part
 * @access  Private (Admin only)
 * @body    { quantity }
 */
sparePartRouter.post('/:id/restock', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.restockSparePart);
/**
 * @route   PUT /api/v1/spare-parts/:id/discontinue
 * @desc    Discontinue a spare part
 * @access  Private (Admin only)
 */
sparePartRouter.put('/:id/discontinue', (0, role_middleware_1.validateRole)([enums_1.UserRole.ADMIN]), spare_part_controller_1.sparePartController.discontinueSparePart);
exports.default = sparePartRouter;
//# sourceMappingURL=spare_part.routes.js.map