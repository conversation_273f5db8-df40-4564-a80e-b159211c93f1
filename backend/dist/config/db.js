"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeDb = exports.connectDb = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const env_config_1 = require("./env_config");
const logger_1 = __importDefault(require("./logger"));
const { connect: _connect, connection: dbConnection } = mongoose_1.default;
const connectDb = async (retries = 5, delay = 5000) => {
    while (retries) {
        try {
            const database_url = env_config_1.config.server.databaseUrl;
            await _connect(database_url);
            logger_1.default.info(`Database connected successfully to : ${database_url.split('@')[1] || database_url}`);
            return;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown database disconnection error ';
            logger_1.default.error(`Failed to connect to the database. Retries left: ${retries}. Error: ${errorMessage}`);
            retries -= 1;
            await new Promise(res => setTimeout(res, delay));
        }
    }
    throw new Error('Database connection failed after multiple retries');
};
exports.connectDb = connectDb;
// Function to gracefully close the database connection
const closeDb = async () => {
    try {
        await dbConnection.close();
        logger_1.default.info('Database connection closed gracefully');
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown database close error ';
        logger_1.default.error(`Error while closing the database connection: ${errorMessage}`);
    }
};
exports.closeDb = closeDb;
//# sourceMappingURL=db.js.map