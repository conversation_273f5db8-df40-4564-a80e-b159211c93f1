"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.admin = exports.NotificationTopic = void 0;
exports.sendPushNotification = sendPushNotification;
exports.subscribeToTopic = subscribeToTopic;
exports.unsubscribeFromTopic = unsubscribeFromTopic;
exports.sendToTopic = sendToTopic;
const admin = __importStar(require("firebase-admin"));
exports.admin = admin;
const app_1 = require("firebase-admin/app");
const env_config_1 = require("../config/env_config");
// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
    admin.initializeApp({
        credential: (0, app_1.cert)({
            projectId: env_config_1.config.firebase.projectId,
            privateKey: env_config_1.config.firebase.privateKey,
            clientEmail: env_config_1.config.firebase.clientEmail,
        }),
    });
}
// Enum for notification topics
var NotificationTopic;
(function (NotificationTopic) {
    NotificationTopic["AGENTS_NEW_ORDER"] = "agents_new_order";
    NotificationTopic["AGENTS_SCHEDULE_UPDATE"] = "agents_schedule_update";
    NotificationTopic["CUSTOMERS_PROMOTIONS"] = "customers_promotions";
    NotificationTopic["CUSTOMERS_SERVICE_ALERTS"] = "customers_service_alerts";
    NotificationTopic["SYSTEM_MAINTENANCE"] = "system_maintenance";
    NotificationTopic["INVENTORY_ALERTS"] = "inventory_alerts";
    NotificationTopic["EMERGENCY_BROADCAST"] = "emergency_broadcast";
})(NotificationTopic || (exports.NotificationTopic = NotificationTopic = {}));
async function sendPushNotification(deviceToken, payload) {
    try {
        console.log('credentials', {
            projectId: env_config_1.config.firebase.projectId,
            clientEmail: env_config_1.config.firebase.clientEmail,
            privateKey: env_config_1.config.firebase.privateKey,
        });
        const message = {
            notification: {
                title: payload.title,
                body: payload.body,
                imageUrl: payload.imageUrl,
            },
            data: payload.data,
            android: payload.android,
            apns: payload.apns,
            webpush: payload.webpush,
            token: Array.isArray(deviceToken) ? deviceToken[0] : deviceToken,
        };
        if (Array.isArray(deviceToken)) {
            // For multiple devices (up to 500 per batch)
            const batchResponse = await admin.messaging().sendEachForMulticast({
                tokens: deviceToken,
                notification: message.notification,
                data: message.data,
                android: message.android,
                apns: message.apns,
                webpush: message.webpush,
            });
            console.log('Successfully sent multicast notification:', batchResponse);
            return batchResponse;
        }
        else {
            // For single device
            const response = await admin.messaging().send(message);
            console.log('Successfully sent notification:', response);
            return response;
        }
    }
    catch (error) {
        console.error('Error sending notification:', error);
        throw error;
    }
}
// Topic management functions
async function subscribeToTopic(deviceTokens, topic) {
    try {
        const response = await admin.messaging().subscribeToTopic(deviceTokens, topic);
        console.log('Successfully subscribed to topic:', response);
        return response;
    }
    catch (error) {
        console.error('Error subscribing to topic:', error);
        throw error;
    }
}
async function unsubscribeFromTopic(deviceTokens, topic) {
    try {
        const response = await admin.messaging().unsubscribeFromTopic(deviceTokens, topic);
        console.log('Successfully unsubscribed from topic:', response);
        return response;
    }
    catch (error) {
        console.error('Error unsubscribing from topic:', error);
        throw error;
    }
}
async function sendToTopic(topic, payload) {
    try {
        const message = {
            notification: {
                title: payload.title,
                body: payload.body,
                imageUrl: payload.imageUrl,
            },
            data: payload.data,
            android: payload.android,
            apns: payload.apns,
            webpush: payload.webpush,
            topic: topic,
        };
        const response = await admin.messaging().send(message);
        console.log('Successfully sent to topic:', response);
        return response;
    }
    catch (error) {
        console.error('Error sending to topic:', error);
        throw error;
    }
}
//# sourceMappingURL=notification_utils.js.map