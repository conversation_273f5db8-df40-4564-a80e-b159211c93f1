"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateOtp = generateOtp;
const crypto_1 = __importDefault(require("crypto"));
function generateOtp() {
    //   return crypto.randomInt(100000, 999999).toString();
    const otp = crypto_1.default.randomInt(100000, 999999).toString(); // Cryptographically secure
    const expirationTime = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes
    return { code: otp, expirationTime };
    // sample output
    // {
    //   code: '123456',
    //   expirationTime: 2025-06-18T14:29:27.417Z
    // }
}
//# sourceMappingURL=otp_utils.js.map