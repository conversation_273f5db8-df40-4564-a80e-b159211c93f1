"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SparePart = void 0;
const enums_1 = require("../enums/enums");
const mongoose_1 = require("mongoose");
const SparePartSchema = new mongoose_1.Schema({
    description: { type: String },
    price: { type: Number, required: true, min: 0 },
    cost: { type: Number, required: true, min: 0 },
    stock: { type: Number, default: 0, min: 0 },
    reserved: { type: Number, default: 0, min: 0 },
    sold: { type: Number, default: 0, min: 0 },
    status: {
        type: String,
        enum: Object.values(enums_1.SparePartStatus),
        default: enums_1.SparePartStatus.AVAILABLE,
    },
    category: {
        type: String,
        enum: Object.values(enums_1.SparePartCategory),
        required: true,
        unique: true, // Make category unique since it replaces name
    },
    compatibleCylinderTypes: [
        {
            type: String,
            enum: Object.values(enums_1.CylinderType),
        },
    ],
    barcode: {
        type: String,
        // unique: true,
        // sparse: true,
    },
    minimumStockLevel: { type: Number, default: 5 },
    lastRestockedAt: { type: Date },
    imageUrl: { type: String }, // Legacy field
    imagePath: { type: String }, // New field for uploaded images
    // Soft delete fields
    isDeleted: { type: Boolean, default: false },
    deletedAt: { type: Date },
    deletedBy: { type: String },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});
// Indexes
SparePartSchema.index({ status: 1 });
SparePartSchema.index({ stock: 1 });
// Note: category index is automatically created due to unique: true constraint
SparePartSchema.index({ compatibleCylinderTypes: 1 });
SparePartSchema.index({ category: 'text', description: 'text', barcode: 'text' }, { weights: { category: 3, barcode: 2, description: 1 } });
// Soft delete indexes
SparePartSchema.index({ isDeleted: 1 }); // Filter deleted items
SparePartSchema.index({ isDeleted: 1, category: 1, status: 1 }); // Combined queries
SparePartSchema.index({ deletedAt: 1 }); // Sort by deletion date
// Virtuals
SparePartSchema.virtual('availableQuantity').get(function () {
    return Math.max(0, this.stock - this.reserved);
});
// Virtual for getting the correct image URL (prioritize uploaded images)
SparePartSchema.virtual('currentImageUrl').get(function () {
    if (this.imagePath) {
        // Remove 'uploads/' prefix for URL serving
        const urlPath = this.imagePath.replace(/^uploads\//, '');
        return `/api/v1/images/${urlPath}`;
    }
    return this.imageUrl; // Fallback to legacy imageUrl
});
SparePartSchema.path('price').validate(function (value) {
    return value >= this.cost;
}, 'Price must be >= cost');
exports.SparePart = (0, mongoose_1.model)('SparePart', SparePartSchema);
//# sourceMappingURL=spareParts.model.js.map